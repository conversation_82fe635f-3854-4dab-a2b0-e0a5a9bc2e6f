#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版成品油网站爬虫
专门处理隆众资讯网站的日评数据
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
from urllib.parse import urljoin
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleOilSpider:
    def __init__(self):
        self.base_url = "https://oil.oilchem.net/444/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        self.daily_review_data = []

    def get_page_content(self, url, max_retries=3):
        """获取页面内容"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return response.text
            except requests.RequestException as e:
                logger.warning(f"获取页面失败 (尝试 {attempt + 1}/{max_retries}): {url}, 错误: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    logger.error(f"获取页面最终失败: {url}")
                    return None

    def extract_daily_review_links_from_main_page(self):
        """从主页面直接提取日评链接"""
        logger.info("从主页面提取日评链接...")
        
        content = self.get_page_content(self.base_url)
        if not content:
            return []
        
        soup = BeautifulSoup(content, 'html.parser')
        daily_review_links = []
        
        # 查找所有链接
        links = soup.find_all('a', href=True)
        
        for link in links:
            link_text = link.get_text(strip=True)
            href = link['href']
            
            # 检查是否包含日评关键词
            if '日评' in link_text:
                # 处理链接
                if href.startswith('//'):
                    href = 'https:' + href
                elif href.startswith('/'):
                    href = 'https://www.oilchem.net' + href
                elif not href.startswith('http'):
                    href = urljoin(self.base_url, href)
                
                daily_review_links.append({
                    'title': link_text,
                    'url': href,
                    'date': self.extract_date_from_title(link_text)
                })
                logger.info(f"找到日评: {link_text}")
        
        logger.info(f"总共找到 {len(daily_review_links)} 个日评链接")
        return daily_review_links

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        # 查找日期模式 (YYYYMMDD)
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            return match.group(1)
        return None

    def extract_basic_info_from_page(self, url, title):
        """从页面提取基本信息（不需要登录的部分）"""
        logger.info(f"提取页面基本信息: {title}")
        
        content = self.get_page_content(url)
        if not content:
            return None
        
        soup = BeautifulSoup(content, 'html.parser')
        
        page_data = {
            'title': title,
            'url': url,
            'date': self.extract_date_from_title(title),
            'content_preview': '',
            'tables_found': 0,
            'login_required': False,
            'extracted_text': []
        }
        
        # 检查是否需要登录
        if self.check_login_required(soup):
            page_data['login_required'] = True
            page_data['content_preview'] = '此页面需要登录才能查看完整内容'
            logger.warning(f"页面需要登录: {title}")
        else:
            # 提取可见的文本内容
            self.extract_visible_content(soup, page_data)
        
        return page_data

    def check_login_required(self, soup):
        """检查是否需要登录"""
        login_indicators = [
            '会员登录', '免费开通', '手机号码', '短信验证码',
            '注册为会员', '致电资讯热线', '400-658-1688'
        ]
        
        page_text = soup.get_text()
        for indicator in login_indicators:
            if indicator in page_text:
                return True
        return False

    def extract_visible_content(self, soup, page_data):
        """提取页面可见内容"""
        try:
            # 查找主要内容区域
            content_areas = soup.find_all(['div', 'article', 'section'], 
                                        class_=re.compile(r'content|article|main', re.I))
            
            if not content_areas:
                # 如果没有找到特定的内容区域，就从body中提取
                content_areas = [soup.find('body')] if soup.find('body') else [soup]
            
            for area in content_areas:
                if area:
                    text = area.get_text(strip=True)
                    if text and len(text) > 50:  # 只保留有意义的文本
                        page_data['extracted_text'].append(text[:500])  # 限制长度
            
            # 查找表格
            tables = soup.find_all('table')
            page_data['tables_found'] = len(tables)
            
            # 提取预览内容
            if page_data['extracted_text']:
                page_data['content_preview'] = page_data['extracted_text'][0][:200] + '...'
            
        except Exception as e:
            logger.warning(f"提取内容时出错: {e}")

    def save_data(self):
        """保存数据到文件"""
        if not self.daily_review_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('daily_review_summary.json', 'w', encoding='utf-8') as f:
            json.dump(self.daily_review_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        df = pd.DataFrame(self.daily_review_data)
        df.to_excel('daily_review_summary.xlsx', index=False, engine='openpyxl')
        
        # 创建详细报告
        self.create_summary_report()
        
        logger.info("数据已保存到 daily_review_summary.json 和 daily_review_summary.xlsx")

    def create_summary_report(self):
        """创建汇总报告"""
        total_links = len(self.daily_review_data)
        login_required = sum(1 for item in self.daily_review_data if item.get('login_required', False))
        accessible = total_links - login_required
        
        report = f"""
# 成品油日评数据爬取报告

## 汇总信息
- 总共找到日评链接: {total_links} 个
- 需要登录的页面: {login_required} 个
- 可直接访问的页面: {accessible} 个

## 详细列表
"""
        
        for i, item in enumerate(self.daily_review_data, 1):
            status = "需要登录" if item.get('login_required', False) else "可访问"
            report += f"{i}. {item['title']} - {status}\n"
            report += f"   链接: {item['url']}\n"
            if item.get('date'):
                report += f"   日期: {item['date']}\n"
            report += f"   预览: {item.get('content_preview', '无')[:100]}...\n\n"
        
        with open('daily_review_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info("详细报告已保存到 daily_review_report.txt")

    def run(self):
        """运行爬虫"""
        logger.info("开始运行简化版成品油日评爬虫...")
        
        # 1. 从主页面提取日评链接
        daily_review_links = self.extract_daily_review_links_from_main_page()
        
        if not daily_review_links:
            logger.warning("未找到任何日评链接")
            return
        
        # 2. 处理每个日评页面
        for i, link_info in enumerate(daily_review_links):
            logger.info(f"处理第 {i+1}/{len(daily_review_links)} 个页面")
            
            page_data = self.extract_basic_info_from_page(link_info['url'], link_info['title'])
            if page_data:
                self.daily_review_data.append(page_data)
            
            time.sleep(1)  # 避免请求过快
        
        # 3. 保存数据
        self.save_data()
        
        logger.info(f"爬虫完成！共处理了 {len(self.daily_review_data)} 个页面")

if __name__ == "__main__":
    spider = SimpleOilSpider()
    spider.run()
