#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作版成品油日评爬虫
基于实际网页结构分析的结果
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
from urllib.parse import urljoin
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WorkingOilSpider:
    def __init__(self):
        self.base_url = "https://oil.oilchem.net/444/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        self.daily_review_data = []

    def get_page_content(self, url, max_retries=3):
        """获取页面内容"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return response.text
            except requests.RequestException as e:
                logger.warning(f"获取页面失败 (尝试 {attempt + 1}/{max_retries}): {url}, 错误: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    logger.error(f"获取页面最终失败: {url}")
                    return None

    def find_daily_review_links(self, max_pages=3):
        """查找日评链接"""
        logger.info("开始查找日评链接...")
        daily_review_links = []
        
        for page in range(1, max_pages + 1):
            if page == 1:
                page_url = self.base_url
            else:
                page_url = f"https://list.oilchem.net/444/{page}.html"
            
            logger.info(f"正在爬取第 {page} 页: {page_url}")
            content = self.get_page_content(page_url)
            
            if not content:
                continue
                
            soup = BeautifulSoup(content, 'html.parser')
            
            # 查找所有链接
            links = soup.find_all('a', href=True)
            
            for link in links:
                link_text = link.get_text(strip=True)
                href = link['href']
                
                # 检查是否包含日评
                if '日评' in link_text:
                    # 处理链接
                    if href.startswith('//'):
                        href = 'https:' + href
                    elif href.startswith('/'):
                        href = 'https://www.oilchem.net' + href
                    elif not href.startswith('http'):
                        href = urljoin(page_url, href)
                    
                    # 检查是否已存在
                    if href not in [item['url'] for item in daily_review_links]:
                        daily_review_links.append({
                            'title': link_text,
                            'url': href,
                            'date': self.extract_date_from_title(link_text)
                        })
                        logger.info(f"找到日评: {link_text}")
            
            time.sleep(1)  # 避免请求过快
        
        logger.info(f"总共找到 {len(daily_review_links)} 个日评链接")
        return daily_review_links

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            return match.group(1)
        return None

    def extract_page_data(self, url, title):
        """提取页面数据"""
        logger.info(f"正在提取页面数据: {title}")
        
        content = self.get_page_content(url)
        if not content:
            return None
        
        soup = BeautifulSoup(content, 'html.parser')
        
        page_data = {
            'title': title,
            'url': url,
            'date': self.extract_date_from_title(title),
            'login_required': False,
            'summary_content': '',
            'market_overview': '',
            'tables': [],
            'full_content': ''
        }
        
        # 检查是否需要登录
        if self.check_login_required(soup):
            page_data['login_required'] = True
            page_data['summary_content'] = '需要登录才能查看完整内容'
            logger.warning(f"页面需要登录: {title}")
            return page_data
        
        # 提取内容
        self.extract_content(soup, page_data)
        
        # 提取表格
        self.extract_tables(soup, page_data)
        
        return page_data

    def check_login_required(self, soup):
        """检查是否需要登录"""
        login_indicators = [
            '会员登录', '免费开通', '手机号码', '短信验证码',
            '注册为会员', '致电资讯热线', '400-658-1688'
        ]
        
        page_text = soup.get_text()
        for indicator in login_indicators:
            if indicator in page_text:
                return True
        return False

    def extract_content(self, soup, page_data):
        """提取页面内容"""
        try:
            # 查找主要内容区域
            main_content = soup.find('div', class_=re.compile(r'content|article|main', re.I))
            if not main_content:
                main_content = soup.find('body')
            
            if main_content:
                full_text = main_content.get_text(strip=True)
                page_data['full_content'] = full_text
                
                # 提取今日摘要到市场概要的内容
                self.extract_summary_and_overview(full_text, page_data)
            
        except Exception as e:
            logger.warning(f"提取内容时出错: {e}")

    def extract_summary_and_overview(self, text, page_data):
        """提取今日摘要和市场概要"""
        try:
            # 查找今日摘要
            summary_match = re.search(r'今日摘要[：:](.*?)(?=市场概要|$)', text, re.DOTALL)
            if summary_match:
                page_data['summary_content'] = summary_match.group(1).strip()
            
            # 查找市场概要
            overview_match = re.search(r'市场概要[：:](.*?)(?=\n\n|$)', text, re.DOTALL)
            if overview_match:
                page_data['market_overview'] = overview_match.group(1).strip()
            
            # 如果没有找到特定标记，尝试提取前几段作为摘要
            if not page_data['summary_content'] and text:
                paragraphs = text.split('\n')
                meaningful_paragraphs = [p.strip() for p in paragraphs if len(p.strip()) > 20]
                if meaningful_paragraphs:
                    page_data['summary_content'] = meaningful_paragraphs[0][:500]
                    
        except Exception as e:
            logger.warning(f"提取摘要时出错: {e}")

    def extract_tables(self, soup, page_data):
        """提取表格数据"""
        try:
            tables = soup.find_all('table')
            
            for i, table in enumerate(tables[:3]):  # 只取前三张表
                table_data = self.parse_table(table, i + 1)
                if table_data:
                    page_data['tables'].append(table_data)
            
            logger.info(f"找到 {len(page_data['tables'])} 张表格")
            
        except Exception as e:
            logger.warning(f"提取表格时出错: {e}")

    def parse_table(self, table, table_index):
        """解析单个表格"""
        try:
            rows = table.find_all('tr')
            if not rows:
                return None
            
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': []
            }
            
            # 提取表头
            if rows:
                header_row = rows[0]
                headers = []
                for cell in header_row.find_all(['th', 'td']):
                    headers.append(cell.get_text(strip=True))
                table_data['headers'] = headers
            
            # 提取数据行
            for row in rows[1:]:
                row_data = []
                for cell in row.find_all(['td', 'th']):
                    row_data.append(cell.get_text(strip=True))
                if row_data:
                    table_data['data'].append(row_data)
            
            return table_data if table_data['data'] else None
            
        except Exception as e:
            logger.warning(f"解析表格 {table_index} 时出错: {e}")
            return None

    def save_data(self):
        """保存数据"""
        if not self.daily_review_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('oil_daily_review_complete.json', 'w', encoding='utf-8') as f:
            json.dump(self.daily_review_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        self.save_to_excel()
        
        # 创建汇总报告
        self.create_report()
        
        logger.info("数据已保存完成")

    def save_to_excel(self):
        """保存到Excel文件"""
        with pd.ExcelWriter('oil_daily_review_complete.xlsx', engine='openpyxl') as writer:
            # 汇总表
            summary_data = []
            for item in self.daily_review_data:
                summary_data.append({
                    '标题': item['title'],
                    '日期': item.get('date', ''),
                    '链接': item['url'],
                    '需要登录': '是' if item.get('login_required', False) else '否',
                    '表格数量': len(item.get('tables', [])),
                    '今日摘要': item.get('summary_content', '')[:100] + '...' if len(item.get('summary_content', '')) > 100 else item.get('summary_content', ''),
                    '市场概要': item.get('market_overview', '')[:100] + '...' if len(item.get('market_overview', '')) > 100 else item.get('market_overview', '')
                })
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='汇总', index=False)
            
            # 为每个页面的表格创建工作表
            for i, item in enumerate(self.daily_review_data):
                for j, table in enumerate(item.get('tables', [])):
                    if table.get('data'):
                        try:
                            df = pd.DataFrame(table['data'], columns=table.get('headers', []))
                            sheet_name = f"页面{i+1}_表格{j+1}"[:31]
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                        except Exception as e:
                            logger.warning(f"保存表格到Excel时出错: {e}")

    def create_report(self):
        """创建详细报告"""
        total = len(self.daily_review_data)
        login_required = sum(1 for item in self.daily_review_data if item.get('login_required', False))
        accessible = total - login_required
        total_tables = sum(len(item.get('tables', [])) for item in self.daily_review_data)
        
        report = f"""# 成品油日评数据爬取报告

## 统计信息
- 总页面数: {total}
- 需要登录: {login_required}
- 可直接访问: {accessible}
- 总表格数: {total_tables}

## 详细信息
"""
        
        for i, item in enumerate(self.daily_review_data, 1):
            status = "需要登录" if item.get('login_required', False) else "可访问"
            report += f"\n{i}. {item['title']}\n"
            report += f"   状态: {status}\n"
            report += f"   日期: {item.get('date', '未知')}\n"
            report += f"   表格数: {len(item.get('tables', []))}\n"
            report += f"   链接: {item['url']}\n"
            
            if item.get('summary_content') and not item.get('login_required'):
                report += f"   摘要: {item['summary_content'][:100]}...\n"
        
        with open('oil_daily_review_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

    def run(self):
        """运行爬虫"""
        logger.info("开始运行成品油日评爬虫...")
        
        # 1. 查找日评链接
        daily_review_links = self.find_daily_review_links()
        
        if not daily_review_links:
            logger.warning("未找到任何日评链接")
            return
        
        # 2. 处理每个页面
        for i, link_info in enumerate(daily_review_links):
            logger.info(f"处理第 {i+1}/{len(daily_review_links)} 个页面")
            
            page_data = self.extract_page_data(link_info['url'], link_info['title'])
            if page_data:
                self.daily_review_data.append(page_data)
            
            time.sleep(2)  # 避免请求过快
        
        # 3. 保存数据
        self.save_data()
        
        logger.info(f"爬虫完成！共处理了 {len(self.daily_review_data)} 个页面")

if __name__ == "__main__":
    spider = WorkingOilSpider()
    spider.run()
