#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium的成品油日评爬虫
解决JavaScript渲染、登录和内容提取问题
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd
import time
import re
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SeleniumOilSpider:
    def __init__(self):
        self.username = "19120333680"
        self.password = "xyz147258369"
        self.base_url = "https://www.oilchem.net/444/"
        
        # 已知的日评链接
        self.daily_review_links = [
            {
                'title': '[华东成品油日评]：成品油零售价搁浅不调 华东汽柴批发价偏弱调整（20250728）',
                'url': 'https://www.oilchem.net/25-0728-16-45626e42b48f2085.html',
                'region': '华东'
            },
            {
                'title': '[华北成品油日评]：国际油价支撑减弱 华北汽柴油价格下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-8c0315b83e481f64.html',
                'region': '华北'
            },
            {
                'title': '[华南成品油日评]：主营赶月度任务为主 汽柴小幅下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-5ca19894c073363d.html',
                'region': '华南'
            }
        ]
        
        self.driver = None
        self.extracted_data = []

    def setup_driver(self):
        """设置Chrome驱动"""
        try:
            chrome_options = Options()
            # 不使用无头模式，这样可以看到登录过程
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 禁用图片加载以提高速度
            prefs = {"profile.managed_default_content_settings.images": 2}
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            logger.info("Chrome驱动设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置Chrome驱动失败: {e}")
            logger.info("请确保已安装Chrome浏览器和ChromeDriver")
            return False

    def perform_login(self):
        """执行登录操作"""
        logger.info("开始登录流程...")
        
        try:
            # 访问主页
            self.driver.get("https://www.oilchem.net/")
            time.sleep(3)
            
            # 查找并点击"点我登录"按钮
            login_selectors = [
                "//a[contains(text(), '点我登录')]",
                "//a[contains(text(), '登录')]",
                "//button[contains(text(), '登录')]",
                ".login-btn",
                "#login-btn",
                "[onclick*='login']"
            ]
            
            login_clicked = False
            for selector in login_selectors:
                try:
                    if selector.startswith("//"):
                        login_element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        login_element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    
                    login_element.click()
                    login_clicked = True
                    logger.info(f"成功点击登录按钮: {selector}")
                    time.sleep(3)
                    break
                except:
                    continue
            
            if not login_clicked:
                logger.warning("未找到登录按钮，尝试直接访问登录页面")
                self.driver.get("https://member.oilchem.net/login/login.html")
                time.sleep(3)
            
            # 查找并填写登录表单
            return self.fill_login_form()
            
        except Exception as e:
            logger.error(f"登录流程出错: {e}")
            return False

    def fill_login_form(self):
        """填写登录表单"""
        try:
            # 查找用户名输入框
            username_selectors = [
                "input[name='username']",
                "input[name='phone']", 
                "input[name='mobile']",
                "input[type='text']",
                "#username",
                "#phone",
                "#mobile"
            ]
            
            username_input = None
            for selector in username_selectors:
                try:
                    username_input = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue
            
            if not username_input:
                logger.error("未找到用户名输入框")
                return False
            
            # 查找密码输入框
            password_selectors = [
                "input[name='password']",
                "input[type='password']",
                "#password"
            ]
            
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except:
                    continue
            
            if not password_input:
                logger.error("未找到密码输入框")
                return False
            
            # 填写用户名和密码
            username_input.clear()
            username_input.send_keys(self.username)
            logger.info("已填写用户名")
            
            password_input.clear()
            password_input.send_keys(self.password)
            logger.info("已填写密码")
            
            # 查找并点击登录按钮
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "//button[contains(text(), '登录')]",
                "//input[contains(@value, '登录')]",
                ".login-submit",
                "#login-submit"
            ]
            
            for selector in submit_selectors:
                try:
                    if selector.startswith("//"):
                        submit_btn = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        submit_btn = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    
                    submit_btn.click()
                    logger.info("已点击登录按钮")
                    time.sleep(5)
                    break
                except:
                    continue
            
            # 检查登录是否成功
            return self.check_login_success()
            
        except Exception as e:
            logger.error(f"填写登录表单失败: {e}")
            return False

    def check_login_success(self):
        """检查登录是否成功"""
        try:
            # 等待页面加载
            time.sleep(5)
            
            # 检查当前URL
            current_url = self.driver.current_url
            logger.info(f"当前URL: {current_url}")
            
            # 检查页面内容
            page_source = self.driver.page_source
            
            # 成功指标
            success_indicators = [
                '登录成功', '欢迎', 'welcome', '个人中心', 
                '我的账户', '退出登录', '会员中心'
            ]
            
            # 失败指标
            failure_indicators = [
                '登录失败', '用户名或密码错误', '验证码错误',
                '账号不存在', '密码错误'
            ]
            
            # 检查失败指标
            for indicator in failure_indicators:
                if indicator in page_source:
                    logger.warning(f"登录失败: 发现失败指标 '{indicator}'")
                    return False
            
            # 检查成功指标
            for indicator in success_indicators:
                if indicator in page_source:
                    logger.info(f"登录成功: 发现成功指标 '{indicator}'")
                    return True
            
            # 如果URL不包含login，可能登录成功
            if 'login' not in current_url.lower():
                logger.info("登录可能成功: URL不包含login")
                return True
            
            logger.warning("登录状态不明确，继续执行")
            return True
            
        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            return False

    def extract_page_content(self, url, title, region):
        """提取页面内容"""
        logger.info(f"正在提取: {title}")
        
        try:
            self.driver.get(url)
            time.sleep(5)  # 等待页面完全加载
            
            page_data = {
                'title': title,
                'url': url,
                'region': region,
                'date': self.extract_date_from_title(title),
                'today_summary': '',
                'market_overview': '',
                'full_summary_to_overview': '',
                'tables': [],
                'extraction_status': 'unknown'
            }
            
            # 检查是否需要登录
            if self.still_needs_login():
                page_data['extraction_status'] = 'login_required'
                page_data['today_summary'] = '页面需要登录才能查看完整内容'
                logger.warning(f"页面需要登录: {title}")
            else:
                page_data['extraction_status'] = 'success'
                # 提取完整内容
                self.extract_summary_to_overview_selenium(page_data)
                # 提取表格
                self.extract_tables_selenium(page_data)
            
            return page_data
            
        except Exception as e:
            logger.error(f"提取页面内容失败: {e}")
            return {
                'title': title,
                'url': url,
                'region': region,
                'date': self.extract_date_from_title(title),
                'today_summary': f'提取失败: {str(e)}',
                'market_overview': '',
                'full_summary_to_overview': '',
                'tables': [],
                'extraction_status': 'error'
            }

    def still_needs_login(self):
        """检查是否仍需要登录"""
        try:
            page_source = self.driver.page_source
            login_indicators = [
                '会员登录', '免费开通', '手机号码', '短信验证码',
                '注册为会员', '致电资讯热线', '400-658-1688'
            ]
            
            return any(indicator in page_source for indicator in login_indicators)
        except:
            return True

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            date_str = match.group(1)
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        return None

    def extract_summary_to_overview_selenium(self, page_data):
        """使用Selenium提取从今日摘要到市场概况的内容"""
        try:
            # 获取页面的完整文本
            page_text = self.driver.find_element(By.TAG_NAME, "body").text
            
            # 方法1: 查找包含"今日摘要"和"市场概况"的元素
            summary_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '今日摘要')]")
            overview_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '市场概况')]")
            
            if summary_elements and overview_elements:
                # 尝试获取两个元素之间的内容
                summary_element = summary_elements[0]
                overview_element = overview_elements[0]
                
                # 获取摘要部分
                summary_text = self.get_text_after_element(summary_element, "今日摘要")
                if summary_text:
                    page_data['today_summary'] = summary_text
                
                # 获取概况部分
                overview_text = self.get_text_after_element(overview_element, "市场概况")
                if overview_text:
                    page_data['market_overview'] = overview_text
                
                # 组合完整内容
                if page_data['today_summary'] or page_data['market_overview']:
                    full_content = ""
                    if page_data['today_summary']:
                        full_content += f"今日摘要：{page_data['today_summary']}"
                    if page_data['market_overview']:
                        if full_content:
                            full_content += "\n\n"
                        full_content += f"市场概况：{page_data['market_overview']}"
                    page_data['full_summary_to_overview'] = full_content
                
                logger.info(f"成功提取内容，摘要长度: {len(page_data['today_summary'])}, 概况长度: {len(page_data['market_overview'])}")
                return
            
            # 方法2: 使用正则表达式从页面文本中提取
            summary_to_overview_pattern = r'今日摘要[：:]?(.*?)市场概况[：:]?(.*?)(?=表格|价格表|结论|$)'
            match = re.search(summary_to_overview_pattern, page_text, re.DOTALL | re.IGNORECASE)
            
            if match:
                summary_part = match.group(1).strip()
                overview_part = match.group(2).strip()
                
                page_data['today_summary'] = summary_part
                page_data['market_overview'] = overview_part
                page_data['full_summary_to_overview'] = f"今日摘要：{summary_part}\n\n市场概况：{overview_part}"
                
                logger.info(f"通过正则表达式提取内容，摘要长度: {len(summary_part)}, 概况长度: {len(overview_part)}")
                return
            
            # 方法3: 查找主要内容区域
            content_selectors = [
                ".content", ".article", ".main", ".news-content",
                ".detail-content", ".article-content", "#content"
            ]
            
            for selector in content_selectors:
                try:
                    content_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    content_text = content_element.text
                    
                    if len(content_text) > 100:  # 确保有足够的内容
                        # 尝试从内容中提取摘要和概况
                        summary_match = re.search(r'今日摘要[：:]?(.*?)(?=市场概况|$)', content_text, re.DOTALL | re.IGNORECASE)
                        if summary_match:
                            page_data['today_summary'] = summary_match.group(1).strip()
                        
                        overview_match = re.search(r'市场概况[：:]?(.*?)(?=表格|价格表|结论|$)', content_text, re.DOTALL | re.IGNORECASE)
                        if overview_match:
                            page_data['market_overview'] = overview_match.group(1).strip()
                        
                        if page_data['today_summary'] or page_data['market_overview']:
                            full_content = ""
                            if page_data['today_summary']:
                                full_content += f"今日摘要：{page_data['today_summary']}"
                            if page_data['market_overview']:
                                if full_content:
                                    full_content += "\n\n"
                                full_content += f"市场概况：{page_data['market_overview']}"
                            page_data['full_summary_to_overview'] = full_content
                            
                            logger.info(f"从内容区域提取成功")
                            return
                except:
                    continue
            
            # 如果都没找到，记录页面的主要文本用于调试
            if len(page_text) > 100:
                page_data['today_summary'] = f"页面文本预览: {page_text[:500]}..."
                page_data['full_summary_to_overview'] = page_data['today_summary']
                logger.info("未找到特定内容，保存页面文本预览")
            
        except Exception as e:
            logger.warning(f"提取摘要到概况内容失败: {e}")

    def get_text_after_element(self, element, keyword):
        """获取元素后面的文本内容"""
        try:
            # 获取元素的父容器
            parent = element.find_element(By.XPATH, "./..")
            parent_text = parent.text
            
            # 查找关键词后的内容
            keyword_index = parent_text.find(keyword)
            if keyword_index != -1:
                after_keyword = parent_text[keyword_index + len(keyword):].strip()
                # 移除开头的冒号
                if after_keyword.startswith('：') or after_keyword.startswith(':'):
                    after_keyword = after_keyword[1:].strip()
                
                # 查找到下一个关键词或段落结束
                end_keywords = ['市场概况', '表格', '价格表', '结论']
                for end_keyword in end_keywords:
                    end_index = after_keyword.find(end_keyword)
                    if end_index != -1:
                        after_keyword = after_keyword[:end_index].strip()
                        break
                
                return after_keyword[:1000]  # 限制长度
            
            return ""
        except:
            return ""

    def extract_tables_selenium(self, page_data):
        """使用Selenium提取表格"""
        try:
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            logger.info(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables[:3]):  # 只取前三张表
                table_data = self.parse_table_selenium(table, i + 1)
                if table_data:
                    page_data['tables'].append(table_data)
            
        except Exception as e:
            logger.warning(f"提取表格失败: {e}")

    def parse_table_selenium(self, table, table_index):
        """使用Selenium解析表格"""
        try:
            rows = table.find_elements(By.TAG_NAME, "tr")
            if not rows:
                return None
            
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': []
            }
            
            # 提取表头
            if rows:
                header_cells = rows[0].find_elements(By.TAG_NAME, "th")
                if not header_cells:
                    header_cells = rows[0].find_elements(By.TAG_NAME, "td")
                
                headers = [cell.text.strip() for cell in header_cells]
                table_data['headers'] = headers
            
            # 提取数据行
            for row in rows[1:]:
                cells = row.find_elements(By.TAG_NAME, "td")
                row_data = [cell.text.strip() for cell in cells]
                if any(row_data):
                    table_data['data'].append(row_data)
            
            return table_data if table_data['data'] else None
            
        except Exception as e:
            logger.warning(f"解析表格失败: {e}")
            return None

    def save_data(self):
        """保存数据"""
        if not self.extracted_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('selenium_oil_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        try:
            with pd.ExcelWriter('selenium_oil_data.xlsx', engine='openpyxl') as writer:
                # 汇总表
                summary_data = []
                for item in self.extracted_data:
                    summary_data.append({
                        '地区': item.get('region', ''),
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '提取状态': item.get('extraction_status', ''),
                        '表格数量': len(item.get('tables', [])),
                        '今日摘要': item.get('today_summary', '')[:300],
                        '市场概况': item.get('market_overview', '')[:300],
                        '完整内容长度': len(item.get('full_summary_to_overview', '')),
                        '链接': item['url']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总', index=False)
                
                # 完整内容表
                full_content_data = []
                for item in self.extracted_data:
                    full_content_data.append({
                        '地区': item.get('region', ''),
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '完整内容': item.get('full_summary_to_overview', ''),
                        '链接': item['url']
                    })
                
                full_content_df = pd.DataFrame(full_content_data)
                full_content_df.to_excel(writer, sheet_name='完整内容', index=False)
                
        except Exception as e:
            logger.error(f"保存Excel失败: {e}")

    def run(self):
        """运行爬虫"""
        logger.info("开始运行Selenium版成品油日评爬虫...")
        
        # 1. 设置驱动
        if not self.setup_driver():
            logger.error("无法设置浏览器驱动，退出")
            return
        
        try:
            # 2. 执行登录
            login_success = self.perform_login()
            if login_success:
                logger.info("登录成功！")
            else:
                logger.warning("登录失败，但继续尝试提取内容")
            
            # 3. 处理每个页面
            for i, link_info in enumerate(self.daily_review_links):
                logger.info(f"处理第 {i+1}/{len(self.daily_review_links)} 个页面")
                
                page_data = self.extract_page_content(
                    link_info['url'], 
                    link_info['title'], 
                    link_info['region']
                )
                
                if page_data:
                    self.extracted_data.append(page_data)
                
                time.sleep(3)
            
            # 4. 保存数据
            self.save_data()
            
            logger.info(f"爬虫完成！共处理了 {len(self.extracted_data)} 个页面")
            logger.info("数据已保存到 selenium_oil_data.json 和 selenium_oil_data.xlsx")
            
        finally:
            # 关闭浏览器
            if self.driver:
                input("按回车键关闭浏览器...")  # 暂停以便查看结果
                self.driver.quit()
                logger.info("浏览器已关闭")

if __name__ == "__main__":
    spider = SeleniumOilSpider()
    spider.run()
