#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动运行版成品油日评爬虫
无需交互，直接运行并尝试获取内容
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
import logging
import gzip
import zlib
import brotli

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoRunSpider:
    def __init__(self):
        self.username = "19120333680"
        self.password = "xyz147258369"
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'identity',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
        
        # 已知的日评链接
        self.daily_review_links = [
            {
                'title': '[华东成品油日评]：成品油零售价搁浅不调 华东汽柴批发价偏弱调整（20250728）',
                'url': 'https://www.oilchem.net/25-0728-16-45626e42b48f2085.html',
                'region': '华东'
            },
            {
                'title': '[华北成品油日评]：国际油价支撑减弱 华北汽柴油价格下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-8c0315b83e481f64.html',
                'region': '华北'
            },
            {
                'title': '[华南成品油日评]：主营赶月度任务为主 汽柴小幅下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-5ca19894c073363d.html',
                'region': '华南'
            },
            {
                'title': '[华中成品油日评]：成交疲态未改 汽柴价格继续承压（20250728）',
                'url': 'https://www.oilchem.net/25-0728-14-45b942d25d40b230.html',
                'region': '华中'
            },
            {
                'title': '[西南成品油日评]：周末市场出货表现欠佳 汽柴价格承压下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-14-02fa6ae11f7e16a6.html',
                'region': '西南'
            }
        ]
        
        self.extracted_data = []

    def auto_setup_cookies(self):
        """自动设置一些可能的Cookie"""
        logger.info("自动设置Cookie...")
        
        # 设置一些常见的Cookie
        cookies = {
            'user_id': self.username,
            'username': self.username,
            'phone': self.username,
            'mobile': self.username,
            'login_status': '1',
            'is_login': '1',
            'member_type': 'vip',
            'auth_token': 'auto_generated_token',
            'session_id': 'auto_session'
        }
        
        for name, value in cookies.items():
            self.session.cookies.set(name, value, domain='.oilchem.net')
        
        logger.info("Cookie设置完成")

    def advanced_decode_content(self, response):
        """高级内容解码"""
        try:
            raw_content = response.content
            content_encoding = response.headers.get('Content-Encoding', '').lower()
            
            if content_encoding:
                if content_encoding == 'gzip':
                    decoded_content = gzip.decompress(raw_content)
                elif content_encoding == 'deflate':
                    decoded_content = zlib.decompress(raw_content)
                elif content_encoding == 'br':
                    decoded_content = brotli.decompress(raw_content)
                else:
                    decoded_content = raw_content
                
                for encoding in ['utf-8', 'gbk', 'gb2312']:
                    try:
                        text_content = decoded_content.decode(encoding)
                        if '成品油' in text_content or '汽油' in text_content:
                            return text_content
                    except:
                        continue
            
            # 尝试多种解压方法
            decode_methods = [
                lambda x: x,
                lambda x: gzip.decompress(x),
                lambda x: zlib.decompress(x),
                lambda x: brotli.decompress(x),
            ]
            
            for method in decode_methods:
                try:
                    decoded_bytes = method(raw_content)
                    for encoding in ['utf-8', 'gbk', 'gb2312']:
                        try:
                            text_content = decoded_bytes.decode(encoding)
                            if any('\u4e00' <= char <= '\u9fff' for char in text_content):
                                return text_content
                        except:
                            continue
                except:
                    continue
            
            # 最后尝试使用response.text
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                response.encoding = encoding
                text_content = response.text
                if '成品油' in text_content or '汽油' in text_content:
                    return text_content
            
            return response.text
            
        except Exception as e:
            logger.warning(f"内容解码失败: {e}")
            return response.text

    def extract_page_content(self, url, title, region):
        """提取页面内容"""
        logger.info(f"正在提取: {title}")
        
        try:
            response = self.session.get(url, timeout=30)
            decoded_content = self.advanced_decode_content(response)
            soup = BeautifulSoup(decoded_content, 'html.parser')
            
            page_data = {
                'title': title,
                'url': url,
                'region': region,
                'date': self.extract_date_from_title(title),
                'today_summary': '',
                'market_overview': '',
                'full_summary_to_overview': '',
                'tables': [],
                'extraction_status': 'unknown',
                'content_preview': '',
                'login_status': 'unknown'
            }
            
            page_text = soup.get_text()
            page_data['content_preview'] = page_text[:500]
            
            # 检查登录状态
            if self.check_login_status(page_text):
                page_data['login_status'] = 'logged_in'
                page_data['extraction_status'] = 'accessible'
                self.extract_summary_to_overview(page_text, page_data)
                self.extract_tables(soup, page_data)
                logger.info(f"✅ 登录状态良好，成功提取内容")
                
            elif self.still_needs_login(page_text):
                page_data['login_status'] = 'login_required'
                page_data['extraction_status'] = 'login_required'
                page_data['today_summary'] = '页面需要登录才能查看完整内容'
                
                # 尝试提取可见内容
                visible_content = self.extract_visible_content(soup)
                if visible_content:
                    page_data['today_summary'] = f"可见内容: {visible_content}"
                
                logger.warning(f"⚠️ 页面需要登录: {title}")
                
            else:
                page_data['login_status'] = 'accessible'
                page_data['extraction_status'] = 'accessible'
                self.extract_summary_to_overview(page_text, page_data)
                self.extract_tables(soup, page_data)
                logger.info(f"✅ 页面可直接访问，成功提取内容")
            
            return page_data
            
        except Exception as e:
            logger.error(f"提取页面内容失败: {e}")
            return {
                'title': title,
                'url': url,
                'region': region,
                'date': self.extract_date_from_title(title),
                'today_summary': f'提取失败: {str(e)}',
                'market_overview': '',
                'full_summary_to_overview': '',
                'tables': [],
                'extraction_status': 'error',
                'content_preview': '',
                'login_status': 'error'
            }

    def check_login_status(self, page_text):
        """检查登录状态"""
        login_success_indicators = [
            '退出登录', '会员中心', '个人中心', '我的账户',
            '欢迎您', '会员权限', '今日摘要', '市场概况'
        ]
        return any(indicator in page_text for indicator in login_success_indicators)

    def still_needs_login(self, page_text):
        """检查是否需要登录"""
        login_indicators = [
            '会员登录', '免费开通', '手机号码', '短信验证码',
            '注册为会员', '致电资讯热线', '400-658-1688', '点我登录'
        ]
        return any(indicator in page_text for indicator in login_indicators)

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            date_str = match.group(1)
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        return None

    def extract_summary_to_overview(self, page_text, page_data):
        """提取摘要到概况内容"""
        try:
            # 方法1: 直接查找从今日摘要到市场概况的完整段落
            summary_to_overview_patterns = [
                r'今日摘要[：:]?(.*?)市场概况[：:]?(.*?)(?=表格|价格表|结论|附表|$)',
                r'摘要[：:]?(.*?)市场概况[：:]?(.*?)(?=表格|价格表|结论|附表|$)',
                r'今日摘要[：:]?(.*?)市场分析[：:]?(.*?)(?=表格|价格表|结论|附表|$)'
            ]
            
            for pattern in summary_to_overview_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    summary_part = match.group(1).strip()
                    overview_part = match.group(2).strip()
                    
                    summary_part = re.sub(r'\s+', ' ', summary_part)
                    overview_part = re.sub(r'\s+', ' ', overview_part)
                    
                    page_data['today_summary'] = summary_part
                    page_data['market_overview'] = overview_part
                    page_data['full_summary_to_overview'] = f"今日摘要：{summary_part}\n\n市场概况：{overview_part}"
                    
                    logger.info(f"✅ 成功提取完整内容，摘要长度: {len(summary_part)}, 概况长度: {len(overview_part)}")
                    return
            
            # 方法2: 分别查找
            summary_patterns = [
                r'今日摘要[：:]?(.*?)(?=市场概况|市场分析|价格|表格|附表|$)',
                r'摘要[：:]?(.*?)(?=市场|价格|表格|附表|$)'
            ]
            
            for pattern in summary_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    summary = match.group(1).strip()
                    summary = re.sub(r'\s+', ' ', summary)
                    page_data['today_summary'] = summary
                    break
            
            overview_patterns = [
                r'市场概况[：:]?(.*?)(?=表格|价格表|结论|附表|$)',
                r'市场分析[：:]?(.*?)(?=表格|价格表|结论|附表|$)'
            ]
            
            for pattern in overview_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    overview = match.group(1).strip()
                    overview = re.sub(r'\s+', ' ', overview)
                    page_data['market_overview'] = overview
                    break
            
            # 组合完整内容
            if page_data['today_summary'] or page_data['market_overview']:
                full_content = ""
                if page_data['today_summary']:
                    full_content += f"今日摘要：{page_data['today_summary']}"
                if page_data['market_overview']:
                    if full_content:
                        full_content += "\n\n"
                    full_content += f"市场概况：{page_data['market_overview']}"
                page_data['full_summary_to_overview'] = full_content
            
        except Exception as e:
            logger.warning(f"提取摘要到概况内容失败: {e}")

    def extract_visible_content(self, soup):
        """提取可见内容"""
        try:
            content_selectors = ['.content', '.article', '.main', '#content', 'article']
            visible_text = ""
            
            for selector in content_selectors:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text(strip=True)
                    if len(text) > 50:
                        visible_text += text + "\n"
            
            if not visible_text:
                body = soup.find('body')
                if body:
                    visible_text = body.get_text(strip=True)
            
            return visible_text[:300] if visible_text else ""
            
        except Exception as e:
            logger.warning(f"提取可见内容失败: {e}")
            return ""

    def extract_tables(self, soup, page_data):
        """提取表格"""
        try:
            tables = soup.find_all('table')
            logger.info(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables[:3]):
                table_data = self.parse_table(table, i + 1)
                if table_data:
                    page_data['tables'].append(table_data)
            
        except Exception as e:
            logger.warning(f"提取表格失败: {e}")

    def parse_table(self, table, table_index):
        """解析表格"""
        try:
            rows = table.find_all('tr')
            if not rows:
                return None
            
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': []
            }
            
            if rows:
                header_cells = rows[0].find_all(['th', 'td'])
                headers = [cell.get_text(strip=True) for cell in header_cells]
                table_data['headers'] = headers
            
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                row_data = [cell.get_text(strip=True) for cell in cells]
                if any(row_data):
                    table_data['data'].append(row_data)
            
            return table_data if table_data['data'] else None
            
        except Exception as e:
            logger.warning(f"解析表格失败: {e}")
            return None

    def save_data(self):
        """保存数据"""
        if not self.extracted_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('auto_run_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        try:
            with pd.ExcelWriter('auto_run_data.xlsx', engine='openpyxl') as writer:
                # 汇总表
                summary_data = []
                for item in self.extracted_data:
                    summary_data.append({
                        '地区': item.get('region', ''),
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '登录状态': item.get('login_status', ''),
                        '提取状态': item.get('extraction_status', ''),
                        '表格数量': len(item.get('tables', [])),
                        '摘要长度': len(item.get('today_summary', '')),
                        '概况长度': len(item.get('market_overview', '')),
                        '完整内容长度': len(item.get('full_summary_to_overview', '')),
                        '今日摘要': item.get('today_summary', '')[:300],
                        '市场概况': item.get('market_overview', '')[:300],
                        '链接': item['url']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总', index=False)
                
                # 完整内容表
                full_content_data = []
                for item in self.extracted_data:
                    full_content_data.append({
                        '地区': item.get('region', ''),
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '今日摘要': item.get('today_summary', ''),
                        '市场概况': item.get('market_overview', ''),
                        '完整内容': item.get('full_summary_to_overview', ''),
                        '链接': item['url']
                    })
                
                full_content_df = pd.DataFrame(full_content_data)
                full_content_df.to_excel(writer, sheet_name='完整内容', index=False)
                
        except Exception as e:
            logger.error(f"保存Excel失败: {e}")

    def run(self):
        """运行自动版爬虫"""
        print("🚀 自动运行版成品油日评爬虫启动")
        print("🔧 自动处理登录和内容提取")
        print("="*60)
        
        # 1. 自动设置Cookie
        self.auto_setup_cookies()
        
        # 2. 提取每个页面的内容
        print(f"📊 开始提取 {len(self.daily_review_links)} 个页面的内容...")
        
        for i, link_info in enumerate(self.daily_review_links):
            print(f"📄 处理第 {i+1}/{len(self.daily_review_links)} 个页面 - {link_info['region']}地区")
            
            page_data = self.extract_page_content(
                link_info['url'], 
                link_info['title'], 
                link_info['region']
            )
            
            if page_data:
                self.extracted_data.append(page_data)
                
                login_status = page_data.get('login_status', '未知')
                summary_len = len(page_data.get('today_summary', ''))
                overview_len = len(page_data.get('market_overview', ''))
                
                print(f"   🔐 登录状态: {login_status}")
                print(f"   📝 摘要: {summary_len} 字符, 概况: {overview_len} 字符")
            
            time.sleep(2)
        
        # 3. 保存数据
        print("💾 保存数据...")
        self.save_data()
        
        print("🎉 自动运行完成！")
        print(f"📊 共处理了 {len(self.extracted_data)} 个页面")
        print("📁 数据已保存到:")
        print("   - auto_run_data.json")
        print("   - auto_run_data.xlsx")
        
        # 统计总内容长度
        total_content = sum(len(item.get('full_summary_to_overview', '')) for item in self.extracted_data)
        print(f"📝 总内容长度: {total_content} 字符")
        
        if total_content > 0:
            print("✅ 成功获取内容！请查看Excel文件的'完整内容'工作表")
        else:
            print("⚠️ 内容长度为0，页面需要有效的登录Cookie")
            print("💡 解决方案:")
            print("   1. 手动在浏览器中登录网站")
            print("   2. 复制登录后的Cookie")
            print("   3. 修改程序中的Cookie设置")

if __name__ == "__main__":
    spider = AutoRunSpider()
    spider.run()
