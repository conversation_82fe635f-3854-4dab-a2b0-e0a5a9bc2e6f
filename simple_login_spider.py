#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化登录爬虫 - 尝试多种登录方式
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleLoginSpider:
    def __init__(self):
        self.username = "19120333680"
        self.password = "xyz147258369"
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        # 测试用的日评链接
        self.test_url = 'https://www.oilchem.net/25-0728-16-45626e42b48f2085.html'
        self.extracted_data = []

    def try_login_methods(self):
        """尝试多种登录方式"""
        logger.info("尝试多种登录方式...")
        
        # 方法1: 尝试POST到常见的登录端点
        login_endpoints = [
            'https://www.oilchem.net/login',
            'https://member.oilchem.net/login',
            'https://www.oilchem.net/api/login',
            'https://member.oilchem.net/api/login',
            'https://www.oilchem.net/member/doLogin',
            'https://member.oilchem.net/doLogin'
        ]
        
        login_data_variants = [
            {'username': self.username, 'password': self.password},
            {'phone': self.username, 'password': self.password},
            {'mobile': self.username, 'password': self.password},
            {'account': self.username, 'password': self.password},
            {'loginName': self.username, 'loginPwd': self.password},
            {'user': self.username, 'pwd': self.password},
        ]
        
        for endpoint in login_endpoints:
            for login_data in login_data_variants:
                try:
                    logger.info(f"尝试登录: {endpoint} with {list(login_data.keys())}")
                    
                    response = self.session.post(endpoint, data=login_data, timeout=30)
                    logger.info(f"响应状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        # 检查响应内容
                        response_text = response.text
                        if any(indicator in response_text for indicator in ['成功', 'success', '欢迎', 'welcome']):
                            logger.info("登录可能成功！")
                            return True
                        elif any(indicator in response_text for indicator in ['失败', 'error', '错误', 'fail']):
                            logger.warning("登录失败")
                        else:
                            logger.info("登录状态不明确，继续测试...")
                    
                except Exception as e:
                    logger.warning(f"登录尝试失败: {e}")
                    continue
        
        # 方法2: 尝试设置认证cookie
        logger.info("尝试设置认证cookie...")
        self.try_set_auth_cookies()
        
        return False

    def try_set_auth_cookies(self):
        """尝试设置可能的认证cookie"""
        # 常见的认证cookie名称
        auth_cookies = [
            {'name': 'user_id', 'value': self.username},
            {'name': 'username', 'value': self.username},
            {'name': 'phone', 'value': self.username},
            {'name': 'mobile', 'value': self.username},
            {'name': 'login_user', 'value': self.username},
            {'name': 'member_id', 'value': self.username},
            {'name': 'auth_token', 'value': 'test_token'},
            {'name': 'session_id', 'value': 'test_session'},
        ]
        
        for cookie in auth_cookies:
            self.session.cookies.set(cookie['name'], cookie['value'], domain='.oilchem.net')

    def test_access(self):
        """测试是否能访问需要登录的页面"""
        logger.info(f"测试访问页面: {self.test_url}")
        
        try:
            response = self.session.get(self.test_url, timeout=30)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查是否仍需要登录
            page_text = soup.get_text()
            login_indicators = [
                '会员登录', '免费开通', '手机号码', '短信验证码',
                '注册为会员', '致电资讯热线', '400-658-1688'
            ]
            
            needs_login = any(indicator in page_text for indicator in login_indicators)
            
            if needs_login:
                logger.warning("页面仍需要登录")
                return False
            else:
                logger.info("页面可以访问！")
                return True
                
        except Exception as e:
            logger.error(f"测试访问失败: {e}")
            return False

    def extract_available_content(self):
        """提取可访问的内容"""
        logger.info("提取可访问的内容...")
        
        # 测试链接列表
        test_links = [
            {
                'title': '[华东成品油日评]：成品油零售价搁浅不调 华东汽柴批发价偏弱调整（20250728）',
                'url': 'https://www.oilchem.net/25-0728-16-45626e42b48f2085.html'
            },
            {
                'title': '[华北成品油日评]：国际油价支撑减弱 华北汽柴油价格下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-8c0315b83e481f64.html'
            },
            {
                'title': '[华南成品油日评]：主营赶月度任务为主 汽柴小幅下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-5ca19894c073363d.html'
            }
        ]
        
        for link_info in test_links:
            page_data = self.extract_page_data(link_info['url'], link_info['title'])
            if page_data:
                self.extracted_data.append(page_data)
            time.sleep(2)

    def extract_page_data(self, url, title):
        """提取页面数据"""
        logger.info(f"提取页面: {title}")
        
        try:
            response = self.session.get(url, timeout=30)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')
            
            page_data = {
                'title': title,
                'url': url,
                'date': self.extract_date_from_title(title),
                'login_required': False,
                'summary_content': '',
                'market_overview': '',
                'tables': [],
                'available_content': ''
            }
            
            # 检查是否需要登录
            page_text = soup.get_text()
            login_indicators = [
                '会员登录', '免费开通', '手机号码', '短信验证码',
                '注册为会员', '致电资讯热线', '400-658-1688'
            ]
            
            needs_login = any(indicator in page_text for indicator in login_indicators)
            
            if needs_login:
                page_data['login_required'] = True
                page_data['summary_content'] = '需要登录才能查看完整内容'
                
                # 尝试提取可见的部分内容
                visible_content = self.extract_visible_content(soup)
                page_data['available_content'] = visible_content
                
                logger.warning(f"页面需要登录: {title}")
            else:
                # 提取完整内容
                self.extract_full_content(soup, page_data)
                logger.info(f"成功提取完整内容: {title}")
            
            return page_data
            
        except Exception as e:
            logger.error(f"提取页面数据失败: {e}")
            return None

    def extract_visible_content(self, soup):
        """提取可见的内容（即使需要登录）"""
        try:
            # 提取标题和可见文本
            title_elem = soup.find('title')
            title_text = title_elem.get_text() if title_elem else ''
            
            # 提取页面中的可见文本段落
            paragraphs = soup.find_all(['p', 'div', 'span'], string=True)
            visible_texts = []
            
            for p in paragraphs:
                text = p.get_text(strip=True)
                if text and len(text) > 10 and '登录' not in text:
                    visible_texts.append(text)
            
            return f"标题: {title_text}\n可见内容: " + '\n'.join(visible_texts[:5])
            
        except Exception as e:
            logger.warning(f"提取可见内容失败: {e}")
            return "无法提取可见内容"

    def extract_full_content(self, soup, page_data):
        """提取完整内容"""
        try:
            page_text = soup.get_text()
            
            # 提取今日摘要
            summary_match = re.search(r'今日摘要[：:](.*?)(?=市场概要|$)', page_text, re.DOTALL)
            if summary_match:
                page_data['summary_content'] = summary_match.group(1).strip()[:500]
            
            # 提取市场概要
            overview_match = re.search(r'市场概要[：:](.*?)(?=\n\n|$)', page_text, re.DOTALL)
            if overview_match:
                page_data['market_overview'] = overview_match.group(1).strip()[:500]
            
            # 提取表格
            tables = soup.find_all('table')
            for i, table in enumerate(tables[:3]):
                table_data = self.parse_table(table, i + 1)
                if table_data:
                    page_data['tables'].append(table_data)
            
            # 如果没有找到特定内容，提取一般内容
            if not page_data['summary_content']:
                paragraphs = [p.strip() for p in page_text.split('\n') if len(p.strip()) > 30]
                if paragraphs:
                    page_data['summary_content'] = paragraphs[0][:300]
            
        except Exception as e:
            logger.warning(f"提取完整内容失败: {e}")

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            return match.group(1)
        return None

    def parse_table(self, table, table_index):
        """解析表格"""
        try:
            rows = table.find_all('tr')
            if not rows:
                return None
            
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': []
            }
            
            # 提取表头
            if rows:
                header_cells = rows[0].find_all(['th', 'td'])
                headers = [cell.get_text(strip=True) for cell in header_cells]
                table_data['headers'] = headers
            
            # 提取数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                row_data = [cell.get_text(strip=True) for cell in cells]
                if any(row_data):
                    table_data['data'].append(row_data)
            
            return table_data if table_data['data'] else None
            
        except Exception as e:
            logger.warning(f"解析表格失败: {e}")
            return None

    def save_data(self):
        """保存数据"""
        if not self.extracted_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('simple_login_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        try:
            with pd.ExcelWriter('simple_login_data.xlsx', engine='openpyxl') as writer:
                # 汇总表
                summary_data = []
                for item in self.extracted_data:
                    summary_data.append({
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '需要登录': '是' if item.get('login_required', False) else '否',
                        '表格数量': len(item.get('tables', [])),
                        '摘要/可见内容': (item.get('summary_content', '') or item.get('available_content', ''))[:200],
                        '链接': item['url']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总', index=False)
        except Exception as e:
            logger.error(f"保存Excel失败: {e}")
        
        # 创建报告
        self.create_report()

    def create_report(self):
        """创建报告"""
        total = len(self.extracted_data)
        login_required = sum(1 for item in self.extracted_data if item.get('login_required', False))
        accessible = total - login_required
        
        report = f"""# 成品油日评数据爬取报告（简化登录版）

## 统计信息
- 总页面数: {total}
- 需要登录: {login_required}
- 可直接访问: {accessible}

## 详细信息
"""
        
        for i, item in enumerate(self.extracted_data, 1):
            status = "需要登录" if item.get('login_required', False) else "可直接访问"
            report += f"\n{i}. {item['title']}\n"
            report += f"   状态: {status}\n"
            report += f"   日期: {item.get('date', '未知')}\n"
            
            if item.get('login_required'):
                content = item.get('available_content', '')
                if content:
                    report += f"   可见内容: {content[:100]}...\n"
            else:
                if item.get('summary_content'):
                    report += f"   摘要: {item['summary_content'][:100]}...\n"
        
        with open('simple_login_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

    def run(self):
        """运行爬虫"""
        logger.info("开始运行简化登录爬虫...")
        
        # 1. 尝试登录
        login_success = self.try_login_methods()
        if login_success:
            logger.info("登录成功！")
        else:
            logger.warning("登录失败，将尝试提取可访问的内容")
        
        # 2. 测试访问
        can_access = self.test_access()
        if can_access:
            logger.info("可以访问受保护的内容")
        else:
            logger.warning("无法访问受保护的内容，将提取可见部分")
        
        # 3. 提取内容
        self.extract_available_content()
        
        # 4. 保存数据
        self.save_data()
        
        logger.info(f"爬虫完成！共处理了 {len(self.extracted_data)} 个页面")

if __name__ == "__main__":
    spider = SimpleLoginSpider()
    spider.run()
