#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找正确的登录页面和方式
"""

import requests
from bs4 import BeautifulSoup
import re

def find_login_info():
    """查找登录相关信息"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    }
    
    # 检查主页面的登录相关信息
    main_url = "https://oil.oilchem.net/444/"
    
    try:
        response = requests.get(main_url, headers=headers, timeout=30)
        response.encoding = 'utf-8'
        soup = BeautifulSoup(response.text, 'html.parser')
        
        print("=== 查找登录相关链接 ===")
        
        # 查找所有包含"登录"的链接
        login_links = soup.find_all('a', text=re.compile(r'登录'))
        for link in login_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)
            print(f"登录链接: {text} -> {href}")
        
        # 查找所有包含"login"的链接
        login_hrefs = soup.find_all('a', href=re.compile(r'login', re.I))
        for link in login_hrefs:
            href = link.get('href', '')
            text = link.get_text(strip=True)
            print(f"Login链接: {text} -> {href}")
        
        # 查找JavaScript中的登录相关代码
        scripts = soup.find_all('script')
        print("\n=== 查找JavaScript中的登录信息 ===")
        for script in scripts:
            if script.string:
                script_content = script.string
                if 'login' in script_content.lower():
                    # 查找登录相关的URL
                    urls = re.findall(r'https?://[^\s"\']+login[^\s"\']*', script_content, re.I)
                    for url in urls:
                        print(f"JS中的登录URL: {url}")
        
        # 检查是否有登录表单
        forms = soup.find_all('form')
        print(f"\n=== 找到 {len(forms)} 个表单 ===")
        for i, form in enumerate(forms):
            action = form.get('action', '')
            method = form.get('method', 'GET')
            print(f"表单 {i+1}: action={action}, method={method}")
            
            # 查找表单中的输入字段
            inputs = form.find_all('input')
            for inp in inputs:
                name = inp.get('name', '')
                type_attr = inp.get('type', '')
                if name:
                    print(f"  输入字段: {name} (type: {type_attr})")
        
        # 检查页面中是否有登录相关的文本
        page_text = soup.get_text()
        if '会员登录' in page_text:
            print("\n=== 页面包含'会员登录'文本 ===")
        
        # 尝试查找可能的登录API端点
        print("\n=== 尝试常见的登录端点 ===")
        possible_endpoints = [
            "https://www.oilchem.net/login",
            "https://member.oilchem.net/login",
            "https://member.oilchem.net/api/login",
            "https://www.oilchem.net/api/login",
            "https://member.oilchem.net/doLogin",
            "https://www.oilchem.net/member/login"
        ]
        
        for endpoint in possible_endpoints:
            try:
                resp = requests.get(endpoint, headers=headers, timeout=10)
                print(f"{endpoint} - 状态码: {resp.status_code}")
                if resp.status_code == 200:
                    # 检查是否包含登录表单
                    soup_endpoint = BeautifulSoup(resp.text, 'html.parser')
                    forms_in_endpoint = soup_endpoint.find_all('form')
                    if forms_in_endpoint:
                        print(f"  -> 包含 {len(forms_in_endpoint)} 个表单")
                        for form in forms_in_endpoint:
                            inputs = form.find_all('input')
                            input_names = [inp.get('name', '') for inp in inputs if inp.get('name')]
                            if any(name in ['username', 'password', 'phone', 'mobile'] for name in input_names):
                                print(f"  -> 可能的登录表单，字段: {input_names}")
            except:
                print(f"{endpoint} - 无法访问")
        
    except Exception as e:
        print(f"查找登录信息时出错: {e}")

if __name__ == "__main__":
    find_login_info()
