#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试版爬虫 - 直接使用已知的日评链接
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import json
import re
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestOilSpider:
    def __init__(self):
        # 基于调试结果的已知日评链接
        self.known_daily_review_links = [
            {
                'title': '[华东成品油日评]：成品油零售价搁浅不调 华东汽柴批发价偏弱调整（20250728）',
                'url': 'https://www.oilchem.net/25-0728-16-45626e42b48f2085.html'
            },
            {
                'title': '[华北成品油日评]：国际油价支撑减弱 华北汽柴油价格下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-8c0315b83e481f64.html'
            },
            {
                'title': '[华南成品油日评]：主营赶月度任务为主 汽柴小幅下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-5ca19894c073363d.html'
            },
            {
                'title': '[华中成品油日评]：成交疲态未改 汽柴价格继续承压（20250728）',
                'url': 'https://www.oilchem.net/25-0728-14-45b942d25d40b230.html'
            },
            {
                'title': '[西南成品油日评]：周末市场出货表现欠佳 汽柴价格承压下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-14-02fa6ae11f7e16a6.html'
            }
        ]
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        })
        self.extracted_data = []

    def get_page_content(self, url):
        """获取页面内容"""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except Exception as e:
            logger.error(f"获取页面失败: {url}, 错误: {e}")
            return None

    def extract_page_data(self, url, title):
        """提取页面数据"""
        logger.info(f"正在提取: {title}")
        
        content = self.get_page_content(url)
        if not content:
            return None
        
        soup = BeautifulSoup(content, 'html.parser')
        
        page_data = {
            'title': title,
            'url': url,
            'date': self.extract_date_from_title(title),
            'login_required': False,
            'summary_content': '',
            'market_overview': '',
            'tables': [],
            'raw_content': ''
        }
        
        # 检查是否需要登录
        if self.check_login_required(soup):
            page_data['login_required'] = True
            page_data['summary_content'] = '需要登录才能查看完整内容'
            logger.warning(f"页面需要登录: {title}")
        else:
            # 提取内容
            self.extract_content(soup, page_data)
            # 提取表格
            self.extract_tables(soup, page_data)
        
        return page_data

    def check_login_required(self, soup):
        """检查是否需要登录"""
        login_indicators = [
            '会员登录', '免费开通', '手机号码', '短信验证码',
            '注册为会员', '致电资讯热线', '400-658-1688'
        ]
        
        page_text = soup.get_text()
        for indicator in login_indicators:
            if indicator in page_text:
                return True
        return False

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            return match.group(1)
        return None

    def extract_content(self, soup, page_data):
        """提取页面内容"""
        try:
            # 获取页面的所有文本
            page_text = soup.get_text()
            page_data['raw_content'] = page_text
            
            # 尝试提取今日摘要
            summary_patterns = [
                r'今日摘要[：:](.*?)(?=市场概要|表格|价格|$)',
                r'摘要[：:](.*?)(?=市场|表格|价格|$)',
                r'概述[：:](.*?)(?=市场|表格|价格|$)'
            ]
            
            for pattern in summary_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    page_data['summary_content'] = match.group(1).strip()[:500]
                    break
            
            # 尝试提取市场概要
            overview_patterns = [
                r'市场概要[：:](.*?)(?=\n\n|表格|价格|$)',
                r'市场[：:](.*?)(?=\n\n|表格|价格|$)'
            ]
            
            for pattern in overview_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    page_data['market_overview'] = match.group(1).strip()[:500]
                    break
            
            # 如果没有找到特定内容，提取前几段作为摘要
            if not page_data['summary_content']:
                paragraphs = [p.strip() for p in page_text.split('\n') if len(p.strip()) > 30]
                if paragraphs:
                    page_data['summary_content'] = paragraphs[0][:300]
            
            logger.info(f"提取内容完成，摘要长度: {len(page_data['summary_content'])}")
            
        except Exception as e:
            logger.warning(f"提取内容时出错: {e}")

    def extract_tables(self, soup, page_data):
        """提取表格"""
        try:
            tables = soup.find_all('table')
            logger.info(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables[:3]):  # 只取前三张表
                table_data = self.parse_table(table, i + 1)
                if table_data:
                    page_data['tables'].append(table_data)
            
            logger.info(f"成功解析 {len(page_data['tables'])} 张表格")
            
        except Exception as e:
            logger.warning(f"提取表格时出错: {e}")

    def parse_table(self, table, table_index):
        """解析表格"""
        try:
            rows = table.find_all('tr')
            if not rows:
                return None
            
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': []
            }
            
            # 提取表头
            if rows:
                header_cells = rows[0].find_all(['th', 'td'])
                headers = [cell.get_text(strip=True) for cell in header_cells]
                table_data['headers'] = headers
            
            # 提取数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                row_data = [cell.get_text(strip=True) for cell in cells]
                if any(row_data):  # 只添加非空行
                    table_data['data'].append(row_data)
            
            return table_data if table_data['data'] else None
            
        except Exception as e:
            logger.warning(f"解析表格 {table_index} 时出错: {e}")
            return None

    def save_data(self):
        """保存数据"""
        if not self.extracted_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('test_oil_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        self.save_to_excel()
        
        # 创建报告
        self.create_report()
        
        logger.info("数据保存完成")

    def save_to_excel(self):
        """保存到Excel"""
        try:
            with pd.ExcelWriter('test_oil_data.xlsx', engine='openpyxl') as writer:
                # 汇总表
                summary_data = []
                for item in self.extracted_data:
                    summary_data.append({
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '需要登录': '是' if item.get('login_required', False) else '否',
                        '表格数量': len(item.get('tables', [])),
                        '今日摘要': item.get('summary_content', '')[:100] + '...' if len(item.get('summary_content', '')) > 100 else item.get('summary_content', ''),
                        '市场概要': item.get('market_overview', '')[:100] + '...' if len(item.get('market_overview', '')) > 100 else item.get('market_overview', ''),
                        '链接': item['url']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总', index=False)
                
                # 表格数据
                for i, item in enumerate(self.extracted_data):
                    for j, table in enumerate(item.get('tables', [])):
                        if table.get('data'):
                            try:
                                df = pd.DataFrame(table['data'], columns=table.get('headers', []))
                                sheet_name = f"页面{i+1}_表格{j+1}"[:31]
                                df.to_excel(writer, sheet_name=sheet_name, index=False)
                            except Exception as e:
                                logger.warning(f"保存表格到Excel时出错: {e}")
        except Exception as e:
            logger.error(f"保存Excel文件时出错: {e}")

    def create_report(self):
        """创建报告"""
        total = len(self.extracted_data)
        login_required = sum(1 for item in self.extracted_data if item.get('login_required', False))
        accessible = total - login_required
        total_tables = sum(len(item.get('tables', [])) for item in self.extracted_data)
        
        report = f"""# 成品油日评数据测试报告

## 统计信息
- 总页面数: {total}
- 需要登录: {login_required}
- 可直接访问: {accessible}
- 总表格数: {total_tables}

## 详细信息
"""
        
        for i, item in enumerate(self.extracted_data, 1):
            status = "需要登录" if item.get('login_required', False) else "可访问"
            report += f"\n{i}. {item['title']}\n"
            report += f"   状态: {status}\n"
            report += f"   日期: {item.get('date', '未知')}\n"
            report += f"   表格数: {len(item.get('tables', []))}\n"
            
            if not item.get('login_required'):
                if item.get('summary_content'):
                    report += f"   摘要: {item['summary_content'][:100]}...\n"
                if item.get('market_overview'):
                    report += f"   概要: {item['market_overview'][:100]}...\n"
        
        with open('test_oil_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

    def run(self):
        """运行测试爬虫"""
        logger.info("开始运行测试爬虫...")
        
        for i, link_info in enumerate(self.known_daily_review_links):
            logger.info(f"处理第 {i+1}/{len(self.known_daily_review_links)} 个页面")
            
            page_data = self.extract_page_data(link_info['url'], link_info['title'])
            if page_data:
                self.extracted_data.append(page_data)
        
        self.save_data()
        logger.info(f"测试完成！共处理了 {len(self.extracted_data)} 个页面")

if __name__ == "__main__":
    spider = TestOilSpider()
    spider.run()
