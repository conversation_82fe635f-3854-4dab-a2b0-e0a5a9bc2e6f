#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版成品油日评爬虫
专门处理隆众资讯网站的特殊情况
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
import logging
from urllib.parse import urljoin

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalOilSpider:
    def __init__(self):
        self.base_url = "https://oil.oilchem.net/444/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        self.extracted_data = []

    def find_daily_review_links(self, max_pages=3):
        """从主页面查找日评链接"""
        logger.info("开始查找日评链接...")
        daily_review_links = []
        
        for page in range(1, max_pages + 1):
            if page == 1:
                page_url = self.base_url
            else:
                page_url = f"https://list.oilchem.net/444/{page}.html"
            
            logger.info(f"正在爬取第 {page} 页: {page_url}")
            
            try:
                response = self.session.get(page_url, timeout=30)
                response.encoding = 'utf-8'
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找所有链接
                links = soup.find_all('a', href=True)
                
                for link in links:
                    link_text = link.get_text(strip=True)
                    href = link['href']
                    
                    # 检查是否包含日评
                    if '日评' in link_text:
                        # 处理链接
                        if href.startswith('//'):
                            href = 'https:' + href
                        elif href.startswith('/'):
                            href = 'https://www.oilchem.net' + href
                        elif not href.startswith('http'):
                            href = urljoin(page_url, href)
                        
                        # 检查是否已存在
                        if href not in [item['url'] for item in daily_review_links]:
                            daily_review_links.append({
                                'title': link_text,
                                'url': href,
                                'date': self.extract_date_from_title(link_text)
                            })
                            logger.info(f"找到日评: {link_text}")
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"爬取页面失败: {e}")
                continue
        
        logger.info(f"总共找到 {len(daily_review_links)} 个日评链接")
        return daily_review_links

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            return match.group(1)
        return None

    def extract_page_data(self, url, title):
        """提取页面数据"""
        logger.info(f"正在提取: {title}")
        
        try:
            response = self.session.get(url, timeout=30)
            
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'iso-8859-1']
            content = None
            
            for encoding in encodings:
                try:
                    response.encoding = encoding
                    content = response.text
                    # 检查是否包含中文字符
                    if '日评' in content or '成品油' in content:
                        logger.info(f"使用编码: {encoding}")
                        break
                except:
                    continue
            
            if not content:
                logger.error(f"无法正确解码页面: {url}")
                return None
            
            soup = BeautifulSoup(content, 'html.parser')
            
            page_data = {
                'title': title,
                'url': url,
                'date': self.extract_date_from_title(title),
                'login_required': False,
                'summary_content': '',
                'market_overview': '',
                'tables': [],
                'page_structure': '',
                'visible_text': ''
            }
            
            # 检查是否需要登录
            if self.check_login_required(soup):
                page_data['login_required'] = True
                page_data['summary_content'] = '需要登录才能查看完整内容'
                
                # 提取可见的标题和基本信息
                self.extract_basic_info(soup, page_data)
                
                logger.warning(f"页面需要登录: {title}")
            else:
                # 提取完整内容
                self.extract_full_content(soup, page_data)
                logger.info(f"成功提取内容: {title}")
            
            return page_data
            
        except Exception as e:
            logger.error(f"提取页面数据失败: {e}")
            return None

    def check_login_required(self, soup):
        """检查是否需要登录"""
        login_indicators = [
            '会员登录', '免费开通', '手机号码', '短信验证码',
            '注册为会员', '致电资讯热线', '400-658-1688'
        ]
        
        page_text = soup.get_text()
        for indicator in login_indicators:
            if indicator in page_text:
                return True
        return False

    def extract_basic_info(self, soup, page_data):
        """提取基本信息（当需要登录时）"""
        try:
            # 提取页面标题
            title_elem = soup.find('title')
            if title_elem:
                page_data['page_structure'] = f"页面标题: {title_elem.get_text()}"
            
            # 提取可见的文本内容
            visible_texts = []
            
            # 查找可能包含内容的元素
            content_selectors = [
                'h1', 'h2', 'h3', '.title', '.content', '.article',
                'p', 'div', 'span'
            ]
            
            for selector in content_selectors:
                elements = soup.select(selector)
                for elem in elements:
                    text = elem.get_text(strip=True)
                    if text and len(text) > 10 and '登录' not in text and '验证码' not in text:
                        visible_texts.append(text)
            
            # 去重并限制长度
            unique_texts = list(dict.fromkeys(visible_texts))
            page_data['visible_text'] = '\n'.join(unique_texts[:10])
            
        except Exception as e:
            logger.warning(f"提取基本信息失败: {e}")

    def extract_full_content(self, soup, page_data):
        """提取完整内容"""
        try:
            # 获取页面文本
            page_text = soup.get_text()
            
            # 清理文本
            cleaned_text = self.clean_text(page_text)
            
            # 提取今日摘要
            summary_patterns = [
                r'今日摘要[：:](.*?)(?=市场概要|表格|价格|结论|$)',
                r'摘要[：:](.*?)(?=市场|表格|价格|结论|$)',
                r'概述[：:](.*?)(?=市场|表格|价格|结论|$)'
            ]
            
            for pattern in summary_patterns:
                match = re.search(pattern, cleaned_text, re.DOTALL | re.IGNORECASE)
                if match:
                    summary = match.group(1).strip()
                    # 清理和截断
                    summary = re.sub(r'\s+', ' ', summary)
                    page_data['summary_content'] = summary[:500]
                    break
            
            # 提取市场概要
            overview_patterns = [
                r'市场概要[：:](.*?)(?=\n\n|表格|价格|结论|$)',
                r'市场分析[：:](.*?)(?=\n\n|表格|价格|结论|$)',
                r'市场[：:](.*?)(?=\n\n|表格|价格|结论|$)'
            ]
            
            for pattern in overview_patterns:
                match = re.search(pattern, cleaned_text, re.DOTALL | re.IGNORECASE)
                if match:
                    overview = match.group(1).strip()
                    overview = re.sub(r'\s+', ' ', overview)
                    page_data['market_overview'] = overview[:500]
                    break
            
            # 如果没有找到特定内容，提取主要段落
            if not page_data['summary_content']:
                paragraphs = self.extract_main_paragraphs(cleaned_text)
                if paragraphs:
                    page_data['summary_content'] = paragraphs[0][:300]
            
            # 提取表格
            self.extract_tables(soup, page_data)
            
        except Exception as e:
            logger.warning(f"提取完整内容失败: {e}")

    def clean_text(self, text):
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,，。：:；;！!？?\(\)\[\]（）【】]', '', text)
        return text.strip()

    def extract_main_paragraphs(self, text):
        """提取主要段落"""
        # 按行分割
        lines = text.split('\n')
        # 过滤有意义的行
        paragraphs = []
        for line in lines:
            line = line.strip()
            if len(line) > 20 and not any(keyword in line for keyword in ['登录', '注册', '验证码', '客服']):
                paragraphs.append(line)
        return paragraphs[:5]

    def extract_tables(self, soup, page_data):
        """提取表格"""
        try:
            tables = soup.find_all('table')
            logger.info(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables[:3]):  # 只取前三张表
                table_data = self.parse_table(table, i + 1)
                if table_data:
                    page_data['tables'].append(table_data)
            
            logger.info(f"成功解析 {len(page_data['tables'])} 张表格")
            
        except Exception as e:
            logger.warning(f"提取表格失败: {e}")

    def parse_table(self, table, table_index):
        """解析表格"""
        try:
            rows = table.find_all('tr')
            if not rows:
                return None
            
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': []
            }
            
            # 提取表头
            if rows:
                header_cells = rows[0].find_all(['th', 'td'])
                headers = [self.clean_text(cell.get_text()) for cell in header_cells]
                table_data['headers'] = headers
            
            # 提取数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                row_data = [self.clean_text(cell.get_text()) for cell in cells]
                if any(row_data):
                    table_data['data'].append(row_data)
            
            return table_data if table_data['data'] else None
            
        except Exception as e:
            logger.warning(f"解析表格失败: {e}")
            return None

    def save_data(self):
        """保存数据"""
        if not self.extracted_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('final_oil_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        try:
            with pd.ExcelWriter('final_oil_data.xlsx', engine='openpyxl') as writer:
                # 汇总表
                summary_data = []
                for item in self.extracted_data:
                    summary_data.append({
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '需要登录': '是' if item.get('login_required', False) else '否',
                        '表格数量': len(item.get('tables', [])),
                        '今日摘要': item.get('summary_content', '')[:200],
                        '市场概要': item.get('market_overview', '')[:200],
                        '可见文本': item.get('visible_text', '')[:200],
                        '链接': item['url']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总', index=False)
                
                # 表格数据
                for i, item in enumerate(self.extracted_data):
                    for j, table in enumerate(item.get('tables', [])):
                        if table.get('data'):
                            try:
                                df = pd.DataFrame(table['data'], columns=table.get('headers', []))
                                sheet_name = f"页面{i+1}_表格{j+1}"[:31]
                                df.to_excel(writer, sheet_name=sheet_name, index=False)
                            except Exception as e:
                                logger.warning(f"保存表格到Excel失败: {e}")
        except Exception as e:
            logger.error(f"保存Excel失败: {e}")
        
        # 创建报告
        self.create_report()

    def create_report(self):
        """创建详细报告"""
        total = len(self.extracted_data)
        login_required = sum(1 for item in self.extracted_data if item.get('login_required', False))
        accessible = total - login_required
        total_tables = sum(len(item.get('tables', [])) for item in self.extracted_data)
        
        report = f"""# 成品油日评数据爬取最终报告

## 统计信息
- 总页面数: {total}
- 需要登录: {login_required}
- 可直接访问: {accessible}
- 总表格数: {total_tables}

## 详细信息
"""
        
        for i, item in enumerate(self.extracted_data, 1):
            status = "需要登录" if item.get('login_required', False) else "可直接访问"
            report += f"\n{i}. {item['title']}\n"
            report += f"   状态: {status}\n"
            report += f"   日期: {item.get('date', '未知')}\n"
            report += f"   表格数: {len(item.get('tables', []))}\n"
            
            if item.get('login_required'):
                visible_text = item.get('visible_text', '')
                if visible_text:
                    report += f"   可见内容: {visible_text[:100]}...\n"
            else:
                if item.get('summary_content'):
                    report += f"   今日摘要: {item['summary_content'][:100]}...\n"
                if item.get('market_overview'):
                    report += f"   市场概要: {item['market_overview'][:100]}...\n"
        
        with open('final_oil_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

    def run(self):
        """运行爬虫"""
        logger.info("开始运行最终版成品油日评爬虫...")
        
        # 1. 查找日评链接
        daily_review_links = self.find_daily_review_links()
        
        if not daily_review_links:
            logger.warning("未找到任何日评链接")
            return
        
        # 2. 处理每个页面
        for i, link_info in enumerate(daily_review_links):
            logger.info(f"处理第 {i+1}/{len(daily_review_links)} 个页面")
            
            page_data = self.extract_page_data(link_info['url'], link_info['title'])
            if page_data:
                self.extracted_data.append(page_data)
            
            time.sleep(2)  # 避免请求过快
        
        # 3. 保存数据
        self.save_data()
        
        logger.info(f"爬虫完成！共处理了 {len(self.extracted_data)} 个页面")

if __name__ == "__main__":
    spider = FinalOilSpider()
    spider.run()
