#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版爬虫 - 用于分析网页结构
"""

import requests
from bs4 import BeautifulSoup
import re

def debug_page_structure():
    """调试页面结构"""
    url = "https://oil.oilchem.net/444/"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.encoding = 'utf-8'
        soup = BeautifulSoup(response.text, 'html.parser')
        
        print("=== 页面标题 ===")
        print(soup.title.text if soup.title else "无标题")
        
        print("\n=== 查找所有包含'日评'的文本 ===")
        all_text = soup.get_text()
        lines_with_daily_review = [line.strip() for line in all_text.split('\n') if '日评' in line and line.strip()]
        for i, line in enumerate(lines_with_daily_review[:10]):  # 只显示前10个
            print(f"{i+1}. {line}")
        
        print(f"\n总共找到 {len(lines_with_daily_review)} 行包含'日评'的文本")
        
        print("\n=== 查找所有链接 ===")
        links = soup.find_all('a', href=True)
        print(f"总共找到 {len(links)} 个链接")
        
        print("\n=== 包含'日评'的链接 ===")
        daily_review_links = []
        for link in links:
            link_text = link.get_text(strip=True)
            href = link['href']
            if '日评' in link_text:
                daily_review_links.append((link_text, href))
                print(f"文本: {link_text}")
                print(f"链接: {href}")
                print("---")
        
        if not daily_review_links:
            print("没有找到包含'日评'的链接")
            
            print("\n=== 查找可能的文章链接 ===")
            article_links = []
            for link in links:
                href = link['href']
                link_text = link.get_text(strip=True)
                # 查找可能的文章链接模式
                if any(pattern in href for pattern in ['/25-', '/24-', '/23-', 'oilchem.net']):
                    if len(link_text) > 10:  # 过滤掉太短的链接文本
                        article_links.append((link_text, href))
            
            print(f"找到 {len(article_links)} 个可能的文章链接")
            for i, (text, href) in enumerate(article_links[:20]):  # 显示前20个
                print(f"{i+1}. {text[:50]}... -> {href}")
        
        print("\n=== 查找列表项 ===")
        list_items = soup.find_all('li')
        print(f"找到 {len(list_items)} 个列表项")
        
        daily_review_in_li = []
        for li in list_items:
            li_text = li.get_text(strip=True)
            if '日评' in li_text:
                daily_review_in_li.append(li_text)
                print(f"列表项: {li_text}")
                # 查找这个列表项中的链接
                li_links = li.find_all('a', href=True)
                for li_link in li_links:
                    print(f"  -> 链接: {li_link['href']}")
                print("---")
        
        if not daily_review_in_li:
            print("列表项中没有找到'日评'")
            
    except Exception as e:
        print(f"调试时出错: {e}")

if __name__ == "__main__":
    debug_page_structure()
