#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门解决内容压缩/编码问题的成品油日评爬虫
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
import logging
import gzip
import zlib
import base64
import brotli
from urllib.parse import urljoin

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DecodeContentSpider:
    def __init__(self):
        self.username = "19120333680"
        self.password = "xyz147258369"
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'identity',  # 禁用压缩
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
        
        # 已知的日评链接
        self.daily_review_links = [
            {
                'title': '[华东成品油日评]：成品油零售价搁浅不调 华东汽柴批发价偏弱调整（20250728）',
                'url': 'https://www.oilchem.net/25-0728-16-45626e42b48f2085.html',
                'region': '华东'
            },
            {
                'title': '[华北成品油日评]：国际油价支撑减弱 华北汽柴油价格下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-8c0315b83e481f64.html',
                'region': '华北'
            },
            {
                'title': '[华南成品油日评]：主营赶月度任务为主 汽柴小幅下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-5ca19894c073363d.html',
                'region': '华南'
            }
        ]
        
        self.extracted_data = []

    def advanced_decode_content(self, response):
        """高级内容解码"""
        logger.info("开始高级内容解码...")
        
        # 获取原始内容
        raw_content = response.content
        
        # 方法1: 检查响应头中的编码信息
        content_encoding = response.headers.get('Content-Encoding', '').lower()
        logger.info(f"Content-Encoding: {content_encoding}")
        
        if content_encoding:
            try:
                if content_encoding == 'gzip':
                    decoded_content = gzip.decompress(raw_content)
                elif content_encoding == 'deflate':
                    decoded_content = zlib.decompress(raw_content)
                elif content_encoding == 'br':
                    decoded_content = brotli.decompress(raw_content)
                else:
                    decoded_content = raw_content
                
                # 尝试解码为文本
                for encoding in ['utf-8', 'gbk', 'gb2312', 'iso-8859-1']:
                    try:
                        text_content = decoded_content.decode(encoding)
                        if '成品油' in text_content or '汽油' in text_content:
                            logger.info(f"成功解码，使用编码: {encoding}")
                            return text_content
                    except:
                        continue
            except Exception as e:
                logger.warning(f"标准解压失败: {e}")
        
        # 方法2: 尝试多种解压方法
        decode_methods = [
            # 直接使用原始内容
            lambda x: x,
            # Gzip解压
            lambda x: gzip.decompress(x),
            # Zlib解压
            lambda x: zlib.decompress(x),
            # Brotli解压
            lambda x: brotli.decompress(x),
            # 去掉前几个字节后再解压（可能有头部信息）
            lambda x: gzip.decompress(x[10:]),
            lambda x: zlib.decompress(x[10:]),
        ]
        
        for i, method in enumerate(decode_methods):
            try:
                decoded_bytes = method(raw_content)
                
                # 尝试不同的文本编码
                for encoding in ['utf-8', 'gbk', 'gb2312', 'iso-8859-1']:
                    try:
                        text_content = decoded_bytes.decode(encoding)
                        # 检查是否包含中文字符
                        if any('\u4e00' <= char <= '\u9fff' for char in text_content):
                            logger.info(f"成功解码，方法 {i+1}，编码: {encoding}")
                            return text_content
                    except:
                        continue
            except Exception as e:
                continue
        
        # 方法3: 如果都失败，尝试使用response.text
        try:
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                response.encoding = encoding
                text_content = response.text
                if '成品油' in text_content or '汽油' in text_content:
                    logger.info(f"使用response.text成功，编码: {encoding}")
                    return text_content
        except:
            pass
        
        logger.warning("所有解码方法都失败")
        return response.text

    def try_login(self):
        """尝试登录"""
        logger.info("尝试登录...")
        
        # 简化的登录尝试
        login_data = {
            'username': self.username,
            'password': self.password,
            'phone': self.username,
            'mobile': self.username
        }
        
        login_urls = [
            'https://www.oilchem.net/login',
            'https://member.oilchem.net/login'
        ]
        
        for url in login_urls:
            try:
                response = self.session.post(url, data=login_data, timeout=30)
                if response.status_code == 200:
                    logger.info(f"登录请求发送成功: {url}")
                    break
            except:
                continue
        
        return True

    def extract_page_with_decode(self, url, title, region):
        """使用解码方法提取页面内容"""
        logger.info(f"正在提取并解码: {title}")
        
        try:
            # 发送请求
            response = self.session.get(url, timeout=30)
            
            # 高级解码
            decoded_content = self.advanced_decode_content(response)
            
            # 解析HTML
            soup = BeautifulSoup(decoded_content, 'html.parser')
            
            page_data = {
                'title': title,
                'url': url,
                'region': region,
                'date': self.extract_date_from_title(title),
                'today_summary': '',
                'market_overview': '',
                'full_summary_to_overview': '',
                'tables': [],
                'extraction_status': 'unknown',
                'content_preview': ''
            }
            
            # 获取页面文本
            page_text = soup.get_text()
            page_data['content_preview'] = page_text[:500]  # 保存前500字符用于调试
            
            # 检查是否需要登录
            if self.still_needs_login(soup):
                page_data['extraction_status'] = 'login_required'
                page_data['today_summary'] = '页面需要登录才能查看完整内容'
                logger.warning(f"页面需要登录: {title}")
            else:
                page_data['extraction_status'] = 'accessible'
                
                # 提取今日摘要到市场概况的内容
                self.extract_summary_to_overview(page_text, page_data)
                
                # 提取表格
                self.extract_tables(soup, page_data)
                
                logger.info(f"成功提取内容，摘要长度: {len(page_data['today_summary'])}, 概况长度: {len(page_data['market_overview'])}")
            
            return page_data
            
        except Exception as e:
            logger.error(f"提取页面内容失败: {e}")
            return {
                'title': title,
                'url': url,
                'region': region,
                'date': self.extract_date_from_title(title),
                'today_summary': f'提取失败: {str(e)}',
                'market_overview': '',
                'full_summary_to_overview': '',
                'tables': [],
                'extraction_status': 'error',
                'content_preview': ''
            }

    def still_needs_login(self, soup):
        """检查是否需要登录"""
        login_indicators = [
            '会员登录', '免费开通', '手机号码', '短信验证码',
            '注册为会员', '致电资讯热线', '400-658-1688'
        ]
        
        page_text = soup.get_text()
        return any(indicator in page_text for indicator in login_indicators)

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            date_str = match.group(1)
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        return None

    def extract_summary_to_overview(self, page_text, page_data):
        """提取今日摘要到市场概况的内容"""
        try:
            # 方法1: 直接查找从今日摘要到市场概况的完整段落
            summary_to_overview_patterns = [
                r'今日摘要[：:]?(.*?)市场概况[：:]?(.*?)(?=表格|价格表|结论|附表|$)',
                r'摘要[：:]?(.*?)市场概况[：:]?(.*?)(?=表格|价格表|结论|附表|$)',
                r'今日摘要[：:]?(.*?)市场分析[：:]?(.*?)(?=表格|价格表|结论|附表|$)'
            ]
            
            for pattern in summary_to_overview_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    summary_part = match.group(1).strip()
                    overview_part = match.group(2).strip()
                    
                    # 清理文本
                    summary_part = re.sub(r'\s+', ' ', summary_part)
                    overview_part = re.sub(r'\s+', ' ', overview_part)
                    
                    page_data['today_summary'] = summary_part
                    page_data['market_overview'] = overview_part
                    page_data['full_summary_to_overview'] = f"今日摘要：{summary_part}\n\n市场概况：{overview_part}"
                    
                    logger.info(f"成功提取完整内容，摘要长度: {len(summary_part)}, 概况长度: {len(overview_part)}")
                    return
            
            # 方法2: 分别查找今日摘要和市场概况
            summary_patterns = [
                r'今日摘要[：:]?(.*?)(?=市场概况|市场分析|价格|表格|附表|$)',
                r'摘要[：:]?(.*?)(?=市场|价格|表格|附表|$)'
            ]
            
            for pattern in summary_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    summary = match.group(1).strip()
                    summary = re.sub(r'\s+', ' ', summary)
                    page_data['today_summary'] = summary
                    logger.info(f"找到今日摘要，长度: {len(summary)}")
                    break
            
            overview_patterns = [
                r'市场概况[：:]?(.*?)(?=表格|价格表|结论|附表|$)',
                r'市场分析[：:]?(.*?)(?=表格|价格表|结论|附表|$)'
            ]
            
            for pattern in overview_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    overview = match.group(1).strip()
                    overview = re.sub(r'\s+', ' ', overview)
                    page_data['market_overview'] = overview
                    logger.info(f"找到市场概况，长度: {len(overview)}")
                    break
            
            # 组合完整内容
            if page_data['today_summary'] or page_data['market_overview']:
                full_content = ""
                if page_data['today_summary']:
                    full_content += f"今日摘要：{page_data['today_summary']}"
                if page_data['market_overview']:
                    if full_content:
                        full_content += "\n\n"
                    full_content += f"市场概况：{page_data['market_overview']}"
                page_data['full_summary_to_overview'] = full_content
                return
            
            # 方法3: 如果都没找到，尝试提取主要内容段落
            main_content = self.extract_main_content_paragraphs(page_text)
            if main_content:
                page_data['today_summary'] = main_content
                page_data['full_summary_to_overview'] = f"主要内容：{main_content}"
                logger.info(f"使用主要内容段落，长度: {len(main_content)}")
            
        except Exception as e:
            logger.warning(f"提取摘要到概况内容失败: {e}")

    def extract_main_content_paragraphs(self, text):
        """提取主要内容段落"""
        try:
            # 按段落分割
            paragraphs = text.split('\n')
            
            # 过滤有意义的段落
            meaningful_paragraphs = []
            keywords = ['汽油', '柴油', '价格', '市场', '成品油', '批发', '零售', '调整', '上涨', '下跌']
            
            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                if (len(paragraph) > 30 and 
                    any(keyword in paragraph for keyword in keywords) and
                    not any(exclude in paragraph for exclude in ['登录', '注册', '验证码', '客服', '联系', '电话'])):
                    meaningful_paragraphs.append(paragraph)
            
            return '\n\n'.join(meaningful_paragraphs[:5]) if meaningful_paragraphs else ''
            
        except Exception as e:
            logger.warning(f"提取主要内容段落失败: {e}")
            return ''

    def extract_tables(self, soup, page_data):
        """提取表格"""
        try:
            tables = soup.find_all('table')
            logger.info(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables[:3]):  # 只取前三张表
                table_data = self.parse_table(table, i + 1)
                if table_data:
                    page_data['tables'].append(table_data)
            
        except Exception as e:
            logger.warning(f"提取表格失败: {e}")

    def parse_table(self, table, table_index):
        """解析表格"""
        try:
            rows = table.find_all('tr')
            if not rows:
                return None
            
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': []
            }
            
            # 提取表头
            if rows:
                header_cells = rows[0].find_all(['th', 'td'])
                headers = [cell.get_text(strip=True) for cell in header_cells]
                table_data['headers'] = headers
            
            # 提取数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                row_data = [cell.get_text(strip=True) for cell in cells]
                if any(row_data):
                    table_data['data'].append(row_data)
            
            return table_data if table_data['data'] else None
            
        except Exception as e:
            logger.warning(f"解析表格失败: {e}")
            return None

    def save_data(self):
        """保存数据"""
        if not self.extracted_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('decoded_oil_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        try:
            with pd.ExcelWriter('decoded_oil_data.xlsx', engine='openpyxl') as writer:
                # 汇总表
                summary_data = []
                for item in self.extracted_data:
                    summary_data.append({
                        '地区': item.get('region', ''),
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '提取状态': item.get('extraction_status', ''),
                        '表格数量': len(item.get('tables', [])),
                        '摘要长度': len(item.get('today_summary', '')),
                        '概况长度': len(item.get('market_overview', '')),
                        '完整内容长度': len(item.get('full_summary_to_overview', '')),
                        '今日摘要': item.get('today_summary', '')[:300],
                        '市场概况': item.get('market_overview', '')[:300],
                        '内容预览': item.get('content_preview', '')[:200],
                        '链接': item['url']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总', index=False)
                
                # 完整内容表
                full_content_data = []
                for item in self.extracted_data:
                    full_content_data.append({
                        '地区': item.get('region', ''),
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '今日摘要': item.get('today_summary', ''),
                        '市场概况': item.get('market_overview', ''),
                        '完整内容': item.get('full_summary_to_overview', ''),
                        '链接': item['url']
                    })
                
                full_content_df = pd.DataFrame(full_content_data)
                full_content_df.to_excel(writer, sheet_name='完整内容', index=False)
                
        except Exception as e:
            logger.error(f"保存Excel失败: {e}")
        
        # 创建报告
        self.create_report()

    def create_report(self):
        """创建报告"""
        total = len(self.extracted_data)
        accessible = sum(1 for item in self.extracted_data if item.get('extraction_status') == 'accessible')
        login_required = sum(1 for item in self.extracted_data if item.get('extraction_status') == 'login_required')
        
        # 统计内容长度
        total_summary_length = sum(len(item.get('today_summary', '')) for item in self.extracted_data)
        total_overview_length = sum(len(item.get('market_overview', '')) for item in self.extracted_data)
        total_content_length = sum(len(item.get('full_summary_to_overview', '')) for item in self.extracted_data)
        
        report = f"""# 解码版成品油日评数据爬取报告

## 爬取概况
- 爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
- 目标: 解决内容压缩问题，获取完整的"今日摘要到市场概况"文字内容
- 账号: {self.username}
- 总页面数: {total}
- 可直接访问: {accessible}
- 需要登录: {login_required}

## 内容统计
- 今日摘要总长度: {total_summary_length} 字符
- 市场概况总长度: {total_overview_length} 字符
- 完整内容总长度: {total_content_length} 字符

## 详细结果
"""
        
        for i, item in enumerate(self.extracted_data, 1):
            report += f"\n### {i}. {item.get('region', '未知')}地区\n"
            report += f"- 标题: {item['title']}\n"
            report += f"- 日期: {item.get('date', '未知')}\n"
            report += f"- 提取状态: {item.get('extraction_status', '未知')}\n"
            
            summary_length = len(item.get('today_summary', ''))
            overview_length = len(item.get('market_overview', ''))
            total_length = len(item.get('full_summary_to_overview', ''))
            
            report += f"- 摘要长度: {summary_length} 字符\n"
            report += f"- 概况长度: {overview_length} 字符\n"
            report += f"- 完整内容长度: {total_length} 字符\n"
            
            if item.get('today_summary') and len(item['today_summary']) > 10:
                report += f"- 摘要预览: {item['today_summary'][:100]}...\n"
            
            if item.get('content_preview'):
                report += f"- 内容预览: {item['content_preview'][:100]}...\n"
            
            report += f"- 链接: {item['url']}\n"
        
        report += f"""
## 技术说明

1. **内容解码**: 使用多种解压和编码方法处理压缩内容
2. **禁用压缩**: 设置Accept-Encoding为identity禁用自动压缩
3. **多重解码**: Gzip、Zlib、Brotli等多种解压方法
4. **编码检测**: UTF-8、GBK、GB2312等多种文本编码

## 数据文件

- decoded_oil_data.json: 完整JSON数据
- decoded_oil_data.xlsx: Excel格式，包含汇总表和完整内容表
- decoded_oil_report.txt: 本报告文件
"""
        
        with open('decoded_oil_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

    def run(self):
        """运行爬虫"""
        logger.info("开始运行解码版成品油日评爬虫...")
        logger.info(f"使用账号: {self.username}")
        
        # 1. 尝试登录
        self.try_login()
        
        # 2. 处理每个页面
        for i, link_info in enumerate(self.daily_review_links):
            logger.info(f"处理第 {i+1}/{len(self.daily_review_links)} 个页面")
            
            page_data = self.extract_page_with_decode(
                link_info['url'], 
                link_info['title'], 
                link_info['region']
            )
            
            if page_data:
                self.extracted_data.append(page_data)
            
            time.sleep(3)
        
        # 3. 保存数据
        self.save_data()
        
        logger.info(f"爬虫完成！共处理了 {len(self.extracted_data)} 个页面")
        logger.info("数据已保存到 decoded_oil_data.json 和 decoded_oil_data.xlsx")

if __name__ == "__main__":
    spider = DecodeContentSpider()
    spider.run()
