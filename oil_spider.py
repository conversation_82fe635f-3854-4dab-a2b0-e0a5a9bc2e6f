#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成品油网站爬虫
爬取含有"日评"的页面，并提取三张表的数据
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
from urllib.parse import urljoin, urlparse
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OilSpider:
    def __init__(self):
        self.base_url = "https://oil.oilchem.net/444/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.daily_review_links = []
        self.extracted_data = []

    def get_page_content(self, url, max_retries=3):
        """获取页面内容"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return response.text
            except requests.RequestException as e:
                logger.warning(f"获取页面失败 (尝试 {attempt + 1}/{max_retries}): {url}, 错误: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    logger.error(f"获取页面最终失败: {url}")
                    return None

    def find_daily_review_links(self, max_pages=5):
        """查找所有含有"日评"的页面链接"""
        logger.info("开始查找含有'日评'的页面链接...")

        for page in range(1, max_pages + 1):
            if page == 1:
                page_url = self.base_url
            else:
                page_url = f"https://list.oilchem.net/444/{page}.html"

            logger.info(f"正在爬取第 {page} 页: {page_url}")
            content = self.get_page_content(page_url)

            if not content:
                continue

            soup = BeautifulSoup(content, 'html.parser')

            # 查找所有包含"日评"的链接 - 改进查找逻辑
            links = soup.find_all('a', href=True)
            for link in links:
                link_text = link.get_text(strip=True)
                href = link['href']

                # 检查链接文本是否包含"日评"
                if '日评' in link_text:
                    # 处理相对链接
                    if href.startswith('//'):
                        href = 'https:' + href
                    elif href.startswith('/'):
                        href = 'https://www.oilchem.net' + href
                    elif not href.startswith('http'):
                        href = urljoin(page_url, href)

                    # 检查是否已经存在
                    existing_urls = [item['url'] for item in self.daily_review_links]
                    if href not in existing_urls:
                        self.daily_review_links.append({
                            'title': link_text,
                            'url': href
                        })
                        logger.info(f"找到日评链接: {link_text}")

                # 额外检查：如果链接指向文章页面且标题可能包含日评信息
                elif href.startswith('https://www.oilchem.net/') and any(keyword in href for keyword in ['25-', '24-', '23-']):
                    # 这些可能是文章链接，我们也收集起来后续检查
                    if href not in [item['url'] for item in self.daily_review_links]:
                        # 尝试从链接周围的文本获取更多信息
                        parent_text = ""
                        if link.parent:
                            parent_text = link.parent.get_text(strip=True)

                        if '日评' in parent_text or '日评' in link_text:
                            self.daily_review_links.append({
                                'title': link_text or parent_text,
                                'url': href
                            })
                            logger.info(f"找到可能的日评链接: {link_text or parent_text}")

            time.sleep(1)  # 避免请求过快

        logger.info(f"总共找到 {len(self.daily_review_links)} 个含有'日评'的链接")
        return self.daily_review_links

    def extract_tables_from_page(self, url, title):
        """从页面中提取三张表的数据"""
        logger.info(f"正在提取页面数据: {title}")

        content = self.get_page_content(url)
        if not content:
            return None

        soup = BeautifulSoup(content, 'html.parser')

        # 检查是否需要登录
        if self.check_login_required(soup):
            logger.warning(f"页面需要登录，跳过: {title}")
            return {
                'title': title,
                'url': url,
                'tables': [],
                'summary_content': '需要登录才能查看',
                'market_overview': '需要登录才能查看',
                'login_required': True
            }

        # 查找页面中的所有表格
        tables = soup.find_all('table')

        page_data = {
            'title': title,
            'url': url,
            'tables': [],
            'summary_content': '',
            'market_overview': '',
            'login_required': False
        }

        # 提取今日摘要到市场概要的内容
        self.extract_summary_content(soup, page_data)

        # 提取表格数据
        for i, table in enumerate(tables[:3]):  # 只取前三张表
            table_data = self.parse_table(table, i + 1)
            if table_data:
                page_data['tables'].append(table_data)

        # 如果没有找到表格，尝试提取其他结构化数据
        if not page_data['tables']:
            self.extract_alternative_data(soup, page_data)

        return page_data

    def check_login_required(self, soup):
        """检查页面是否需要登录"""
        # 检查常见的登录提示
        login_indicators = [
            '会员登录', '免费开通', '手机号码', '短信验证码',
            '注册为会员', '致电资讯热线', '400-658-1688'
        ]

        page_text = soup.get_text()
        for indicator in login_indicators:
            if indicator in page_text:
                return True
        return False

    def extract_alternative_data(self, soup, page_data):
        """当没有表格时，尝试提取其他结构化数据"""
        try:
            # 查找价格相关的div或其他容器
            price_containers = soup.find_all(['div', 'span', 'p'],
                                           class_=re.compile(r'price|data|table', re.I))

            if price_containers:
                alt_data = {
                    'table_index': 1,
                    'headers': ['数据类型', '内容'],
                    'data': []
                }

                for container in price_containers[:10]:  # 限制数量
                    text = container.get_text(strip=True)
                    if text and len(text) > 5:  # 过滤太短的文本
                        alt_data['data'].append(['价格信息', text])

                if alt_data['data']:
                    page_data['tables'].append(alt_data)

        except Exception as e:
            logger.warning(f"提取替代数据时出错: {e}")

    def extract_summary_content(self, soup, page_data):
        """提取今日摘要到市场概要的内容"""
        try:
            # 查找包含"今日摘要"的元素
            summary_element = soup.find(text=re.compile(r'今日摘要'))
            if summary_element:
                # 获取父元素
                parent = summary_element.parent
                if parent:
                    # 获取从今日摘要开始的文本内容
                    content_parts = []
                    current = parent
                    
                    # 向下查找内容直到遇到"市场概要"
                    while current:
                        text = current.get_text(strip=True) if hasattr(current, 'get_text') else str(current).strip()
                        if text and '市场概要' in text:
                            # 找到市场概要，截止
                            break
                        if text and text not in content_parts:
                            content_parts.append(text)
                        current = current.next_sibling
                    
                    page_data['summary_content'] = '\n'.join(content_parts)
            
            # 查找市场概要内容
            market_element = soup.find(text=re.compile(r'市场概要'))
            if market_element:
                parent = market_element.parent
                if parent:
                    page_data['market_overview'] = parent.get_text(strip=True)
                    
        except Exception as e:
            logger.warning(f"提取摘要内容时出错: {e}")

    def parse_table(self, table, table_index):
        """解析表格数据"""
        try:
            rows = table.find_all('tr')
            if not rows:
                return None
                
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': []
            }
            
            # 提取表头
            header_row = rows[0]
            headers = []
            for th in header_row.find_all(['th', 'td']):
                headers.append(th.get_text(strip=True))
            table_data['headers'] = headers
            
            # 提取数据行
            for row in rows[1:]:
                row_data = []
                for td in row.find_all(['td', 'th']):
                    row_data.append(td.get_text(strip=True))
                if row_data:  # 只添加非空行
                    table_data['data'].append(row_data)
            
            return table_data
            
        except Exception as e:
            logger.warning(f"解析表格 {table_index} 时出错: {e}")
            return None

    def save_data_to_files(self):
        """保存数据到文件"""
        # 保存为JSON格式
        with open('oil_daily_review_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel格式
        with pd.ExcelWriter('oil_daily_review_data.xlsx', engine='openpyxl') as writer:
            # 创建汇总表
            summary_data = []
            for item in self.extracted_data:
                summary_data.append({
                    '标题': item['title'],
                    '链接': item['url'],
                    '表格数量': len(item['tables']),
                    '今日摘要': item['summary_content'][:100] + '...' if len(item['summary_content']) > 100 else item['summary_content'],
                    '市场概要': item['market_overview'][:100] + '...' if len(item['market_overview']) > 100 else item['market_overview']
                })
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='汇总', index=False)
            
            # 为每个页面的表格创建单独的工作表
            for i, item in enumerate(self.extracted_data):
                for j, table in enumerate(item['tables']):
                    if table['data']:
                        df = pd.DataFrame(table['data'], columns=table['headers'])
                        sheet_name = f"页面{i+1}_表格{j+1}"[:31]  # Excel工作表名称限制
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info("数据已保存到 oil_daily_review_data.json 和 oil_daily_review_data.xlsx")

    def run(self):
        """运行爬虫"""
        logger.info("开始运行成品油日评爬虫...")
        
        # 1. 查找所有日评链接
        self.find_daily_review_links()
        
        if not self.daily_review_links:
            logger.warning("未找到任何日评链接")
            return
        
        # 2. 爬取每个日评页面的数据
        for i, link_info in enumerate(self.daily_review_links):
            logger.info(f"正在处理第 {i+1}/{len(self.daily_review_links)} 个页面")
            
            page_data = self.extract_tables_from_page(link_info['url'], link_info['title'])
            if page_data:
                self.extracted_data.append(page_data)
            
            time.sleep(2)  # 避免请求过快
        
        # 3. 保存数据
        if self.extracted_data:
            self.save_data_to_files()
            logger.info(f"爬虫完成！共处理了 {len(self.extracted_data)} 个页面")
        else:
            logger.warning("未提取到任何数据")

if __name__ == "__main__":
    spider = OilSpider()
    spider.run()
