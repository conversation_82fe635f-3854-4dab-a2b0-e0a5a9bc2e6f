# 成品油日评数据爬虫

这个爬虫程序用于爬取 https://oil.oilchem.net/444/ 网站中所有含有"日评"的页面，并提取其中的表格数据。

## 功能特点

1. **自动查找日评页面**: 自动搜索网站中所有包含"日评"关键词的文章链接
2. **数据提取**: 从每个日评页面中提取：
   - 今日摘要到市场概要的文本内容
   - 前三张表格的完整数据
3. **多格式输出**: 同时生成JSON和Excel格式的数据文件
4. **错误处理**: 包含重试机制和错误日志记录
5. **友好爬取**: 设置了合理的请求间隔，避免对服务器造成压力

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

```bash
python oil_spider.py
```

## 输出文件

运行完成后会生成以下文件：

1. **oil_daily_review_data.json**: 包含所有原始数据的JSON文件
2. **oil_daily_review_data.xlsx**: Excel格式的数据文件，包含：
   - 汇总工作表：所有页面的基本信息
   - 各个表格的详细数据工作表

## 数据结构

### JSON文件结构
```json
[
  {
    "title": "页面标题",
    "url": "页面链接",
    "summary_content": "今日摘要内容",
    "market_overview": "市场概要内容",
    "tables": [
      {
        "table_index": 1,
        "headers": ["列1", "列2", "列3"],
        "data": [
          ["数据1", "数据2", "数据3"],
          ["数据4", "数据5", "数据6"]
        ]
      }
    ]
  }
]
```

## 配置选项

可以在代码中修改以下参数：

- `max_pages`: 爬取的页面数量（默认5页）
- 请求间隔时间
- User-Agent等请求头信息

## 注意事项

1. 请遵守网站的robots.txt规则
2. 不要过于频繁地请求，避免给服务器造成压力
3. 如果遇到反爬虫机制，可能需要添加更多的反反爬虫措施
4. 网站结构可能会发生变化，届时需要相应调整代码

## 错误处理

程序包含了完善的错误处理机制：
- 网络请求失败时会自动重试
- 解析错误会记录日志但不会中断程序
- 所有操作都有详细的日志输出

## 技术栈

- Python 3.6+
- requests: HTTP请求库
- BeautifulSoup4: HTML解析库
- pandas: 数据处理库
- openpyxl: Excel文件处理库
