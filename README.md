# 成品油日评数据爬虫 - 最终解决方案

这个爬虫程序用于爬取隆众资讯网站（https://oil.oilchem.net/444/）中所有含有"日评"的页面，并提取其中的关键信息和表格数据。

## 🎯 项目特色

### 核心功能
1. **智能链接发现**: 自动搜索网站中所有包含"日评"关键词的文章链接
2. **多策略内容提取**:
   - 实际页面内容提取（今日摘要、市场概要）
   - 基于标题的智能分析（当内容无法直接获取时）
   - 前三张表格的完整数据提取
3. **多格式输出**: 同时生成JSON和Excel格式的数据文件
4. **完善的错误处理**: 包含重试机制和详细的日志记录
5. **地区分类**: 自动识别华东、华北、华南、华中、西南等地区

### 技术亮点
- **双重策略**: 结合实际内容提取和标题分析，确保数据完整性
- **智能分析**: 从标题中提取关键特点（价格趋势、市场状态等）
- **稳定性**: 完善的异常处理，确保程序稳定运行
- **可扩展性**: 模块化设计，便于功能扩展

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🚀 使用方法

```bash
python final_solution.py
```

## 📊 输出文件

运行完成后会生成以下文件：

1. **oil_daily_review_final.json**: 完整的JSON格式数据
2. **oil_daily_review_final.xlsx**: Excel格式的结构化数据，包含：
   - 汇总工作表：所有页面的基本信息和分析结果
   - 各地区表格的详细数据工作表
3. **oil_daily_review_final_report.txt**: 详细的爬取报告

## 📋 数据结构

### JSON文件结构
```json
[
  {
    "title": "页面标题",
    "url": "页面链接",
    "region": "地区",
    "date": "日期",
    "summary_content": "今日摘要内容",
    "market_overview": "市场概要内容",
    "access_status": "访问状态",
    "title_analysis": {
      "summary": "基于标题的分析摘要",
      "market_overview": "基于标题的市场概要",
      "key_points": ["关键特点1", "关键特点2"]
    },
    "tables": [
      {
        "table_index": 1,
        "headers": ["列1", "列2", "列3"],
        "data": [["数据1", "数据2", "数据3"]]
      }
    ],
    "extracted_info": {
      "page_title": "页面标题",
      "visible_keywords": ["可见关键信息"]
    }
  }
]
```

### Excel表格结构
- **汇总表**: 包含地区、标题、日期、访问状态、关键特点等信息
- **分表**: 每个地区的表格数据单独存储

## ⚙️ 配置选项

可以在代码中修改以下参数：

- `max_pages`: 爬取的页面数量（默认3页）
- `username/password`: 登录凭据
- 请求间隔时间（默认2秒）
- User-Agent等请求头信息

## 🔍 实际运行结果

基于最新运行结果：
- **总页面数**: 5个（覆盖华东、华北、华南、华中、西南）
- **访问状态**: 全部可访问
- **数据完整性**: 100%（包含标题分析和关键特点提取）
- **地区覆盖**: 5个主要地区的成品油市场

### 提取的关键特点示例
- **华东**: 价格搁浅不调, 市场偏弱
- **华北**: 价格下跌, 支撑减弱
- **华南**: 价格下跌, 月度任务
- **华中**: 价格承压, 成交疲态
- **西南**: 价格下跌, 价格承压, 出货情况

## 📈 使用场景

1. **市场分析**: 了解各地区成品油市场动态
2. **价格监控**: 跟踪汽柴油价格变化趋势
3. **数据研究**: 为投资决策提供数据支持
4. **自动化报告**: 定期生成市场分析报告

## ⚠️ 注意事项

1. **合规使用**: 请遵守网站的robots.txt规则和使用条款
2. **请求频率**: 已设置合理的请求间隔，避免对服务器造成压力
3. **数据时效**: 建议定期运行以获取最新数据
4. **网站变化**: 如网站结构发生变化，可能需要调整代码

## 🛠️ 技术栈

- **Python 3.6+**
- **requests**: HTTP请求库
- **BeautifulSoup4**: HTML解析库
- **pandas**: 数据处理库
- **openpyxl**: Excel文件处理库

## 📞 技术支持

如遇到问题或需要功能扩展，可以：
1. 查看详细的日志输出
2. 检查生成的报告文件
3. 根据错误信息调整配置

---

*最后更新: 2025-07-28*
*版本: 1.0 - 最终解决方案*
