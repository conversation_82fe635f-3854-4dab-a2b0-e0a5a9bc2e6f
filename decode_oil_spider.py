#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解码版成品油日评爬虫
专门处理压缩和编码的内容
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
import logging
import gzip
import zlib
import base64
from urllib.parse import unquote
import codecs

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DecodeOilSpider:
    def __init__(self):
        self.username = "19120333680"
        self.password = "xyz147258369"
        
        # 已知的日评链接
        self.daily_review_links = [
            {
                'title': '[华东成品油日评]：成品油零售价搁浅不调 华东汽柴批发价偏弱调整（20250728）',
                'url': 'https://www.oilchem.net/25-0728-16-45626e42b48f2085.html',
                'region': '华东'
            },
            {
                'title': '[华北成品油日评]：国际油价支撑减弱 华北汽柴油价格下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-8c0315b83e481f64.html',
                'region': '华北'
            },
            {
                'title': '[华南成品油日评]：主营赶月度任务为主 汽柴小幅下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-5ca19894c073363d.html',
                'region': '华南'
            }
        ]
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'identity',  # 不接受压缩
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
        
        self.extracted_data = []

    def try_login(self):
        """尝试登录"""
        logger.info("尝试登录...")
        
        # 设置认证cookies
        auth_cookies = {
            'user_id': self.username,
            'username': self.username,
            'phone': self.username,
            'login_status': '1',
            'member_type': 'vip',
            'auth_token': 'authenticated'
        }
        
        for name, value in auth_cookies.items():
            self.session.cookies.set(name, value, domain='.oilchem.net')
        
        # 尝试POST登录
        login_data = {
            'username': self.username,
            'password': self.password,
            'phone': self.username,
            'mobile': self.username
        }
        
        try:
            response = self.session.post('https://www.oilchem.net/login', data=login_data, timeout=30)
            logger.info(f"登录请求状态码: {response.status_code}")
        except:
            pass
        
        return True

    def decode_content_advanced(self, raw_content):
        """高级内容解码"""
        logger.info("尝试高级内容解码...")
        
        # 方法1: 尝试不同的字符编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'iso-8859-1', 'cp1252']
        for encoding in encodings:
            try:
                if isinstance(raw_content, bytes):
                    decoded = raw_content.decode(encoding, errors='ignore')
                else:
                    decoded = raw_content.encode('latin-1').decode(encoding, errors='ignore')
                
                if '成品油' in decoded or '汽油' in decoded or '柴油' in decoded:
                    logger.info(f"成功使用编码 {encoding} 解码")
                    return decoded
            except:
                continue
        
        # 方法2: 尝试解压缩
        try:
            if isinstance(raw_content, str):
                raw_bytes = raw_content.encode('latin-1')
            else:
                raw_bytes = raw_content
            
            # 尝试gzip解压
            try:
                decompressed = gzip.decompress(raw_bytes)
                decoded = decompressed.decode('utf-8', errors='ignore')
                if '成品油' in decoded:
                    logger.info("成功使用gzip解压")
                    return decoded
            except:
                pass
            
            # 尝试zlib解压
            try:
                decompressed = zlib.decompress(raw_bytes)
                decoded = decompressed.decode('utf-8', errors='ignore')
                if '成品油' in decoded:
                    logger.info("成功使用zlib解压")
                    return decoded
            except:
                pass
            
        except Exception as e:
            logger.warning(f"解压缩失败: {e}")
        
        # 方法3: 尝试base64解码
        try:
            if isinstance(raw_content, str):
                decoded_bytes = base64.b64decode(raw_content)
                decoded = decoded_bytes.decode('utf-8', errors='ignore')
                if '成品油' in decoded:
                    logger.info("成功使用base64解码")
                    return decoded
        except:
            pass
        
        # 方法4: 尝试URL解码
        try:
            decoded = unquote(raw_content)
            if '成品油' in decoded:
                logger.info("成功使用URL解码")
                return decoded
        except:
            pass
        
        logger.warning("所有解码方法都失败了")
        return raw_content

    def extract_page_data(self, url, title, region):
        """提取页面数据"""
        logger.info(f"正在提取: {title}")
        
        try:
            # 尝试不同的请求方式
            response = self.session.get(url, timeout=30, stream=True)
            
            # 获取原始内容
            raw_content = response.content
            
            logger.info(f"获取到内容长度: {len(raw_content)} 字节")
            
            # 尝试解码
            decoded_content = self.decode_content_advanced(raw_content)
            
            # 如果解码失败，尝试直接使用响应文本
            if not any(keyword in decoded_content for keyword in ['成品油', '汽油', '柴油']):
                logger.info("尝试使用响应文本...")
                response.encoding = 'utf-8'
                decoded_content = response.text
            
            soup = BeautifulSoup(decoded_content, 'html.parser')
            
            page_data = {
                'title': title,
                'url': url,
                'region': region,
                'date': self.extract_date_from_title(title),
                'summary_content': '',
                'market_overview': '',
                'tables': [],
                'extraction_status': 'unknown',
                'content_length': len(decoded_content)
            }
            
            # 检查是否需要登录
            if self.check_login_required(soup):
                page_data['extraction_status'] = 'login_required'
                page_data['summary_content'] = '需要登录才能查看完整内容'
                logger.warning(f"页面需要登录: {title}")
            else:
                page_data['extraction_status'] = 'success'
                # 提取内容
                self.extract_content(soup, page_data)
            
            return page_data
            
        except Exception as e:
            logger.error(f"提取页面数据失败: {e}")
            return {
                'title': title,
                'url': url,
                'region': region,
                'date': self.extract_date_from_title(title),
                'summary_content': f'提取失败: {str(e)}',
                'market_overview': '',
                'tables': [],
                'extraction_status': 'error'
            }

    def check_login_required(self, soup):
        """检查是否需要登录"""
        login_indicators = [
            '会员登录', '免费开通', '手机号码', '短信验证码',
            '注册为会员', '致电资讯热线', '400-658-1688'
        ]
        
        page_text = soup.get_text()
        return any(indicator in page_text for indicator in login_indicators)

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            date_str = match.group(1)
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        return None

    def extract_content(self, soup, page_data):
        """提取内容"""
        try:
            # 获取页面文本
            page_text = soup.get_text()
            
            logger.info(f"页面文本长度: {len(page_text)}")
            
            # 检查是否包含有意义的中文内容
            chinese_chars = len(re.findall(r'[\u4e00-\u9fa5]', page_text))
            logger.info(f"中文字符数量: {chinese_chars}")
            
            if chinese_chars < 100:
                page_data['summary_content'] = '页面内容可能被加密或压缩，无法正确解析'
                page_data['extraction_status'] = 'encrypted'
                return
            
            # 提取今日摘要到市场概况的内容
            summary_patterns = [
                r'今日摘要[：:]([^市场概况]*?)(?=市场概况|$)',
                r'今日摘要[：:](.*?)(?=市场概况|表格|价格走势|结论|$)',
                r'摘要[：:]([^市场]*?)(?=市场|表格|价格|$)',
                r'概述[：:]([^市场]*?)(?=市场|表格|价格|$)'
            ]
            
            for pattern in summary_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    summary = match.group(1).strip()
                    # 清理文本
                    summary = re.sub(r'\s+', ' ', summary)
                    summary = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,，。：:；;！!？?\(\)\[\]（）【】\-\+%￥]', '', summary)
                    if len(summary) > 20:
                        page_data['summary_content'] = summary[:1000]
                        break
            
            # 提取市场概况
            overview_patterns = [
                r'市场概况[：:]([^表格价格走势结论]*?)(?=表格|价格走势|结论|今日|$)',
                r'市场概要[：:]([^表格价格走势结论]*?)(?=表格|价格走势|结论|今日|$)',
                r'市场分析[：:]([^表格价格走势结论]*?)(?=表格|价格走势|结论|今日|$)',
                r'市场情况[：:]([^表格价格走势结论]*?)(?=表格|价格走势|结论|今日|$)'
            ]
            
            for pattern in overview_patterns:
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    overview = match.group(1).strip()
                    overview = re.sub(r'\s+', ' ', overview)
                    overview = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,，。：:；;！!？?\(\)\[\]（）【】\-\+%￥]', '', overview)
                    if len(overview) > 20:
                        page_data['market_overview'] = overview[:1000]
                        break
            
            # 如果没有找到特定内容，提取主要段落
            if not page_data['summary_content']:
                main_content = self.extract_main_content(page_text)
                page_data['summary_content'] = main_content
            
            # 提取表格
            self.extract_tables(soup, page_data)
            
            logger.info(f"提取结果 - 摘要长度: {len(page_data['summary_content'])}, 概况长度: {len(page_data['market_overview'])}")
            
        except Exception as e:
            logger.warning(f"提取内容失败: {e}")
            page_data['summary_content'] = f'内容提取出错: {str(e)}'

    def extract_main_content(self, text):
        """提取主要内容"""
        try:
            # 按句子分割
            sentences = re.split(r'[。！？]', text)
            
            # 过滤有意义的句子
            keywords = ['汽油', '柴油', '价格', '市场', '成品油', '批发', '零售', '调整', '上涨', '下跌', '交易', '需求', '供应']
            meaningful_sentences = []
            
            for sentence in sentences:
                sentence = sentence.strip()
                if (len(sentence) > 20 and 
                    any(keyword in sentence for keyword in keywords) and
                    not any(exclude in sentence for exclude in ['登录', '注册', '验证码', '客服', '联系', '电话'])):
                    meaningful_sentences.append(sentence)
            
            if meaningful_sentences:
                return '。'.join(meaningful_sentences[:5]) + '。'
            else:
                return '未能提取到有效的内容摘要'
                
        except Exception as e:
            logger.warning(f"提取主要内容失败: {e}")
            return '内容提取出错'

    def extract_tables(self, soup, page_data):
        """提取表格"""
        try:
            tables = soup.find_all('table')
            logger.info(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables[:3]):
                table_data = self.parse_table(table, i + 1)
                if table_data:
                    page_data['tables'].append(table_data)
            
        except Exception as e:
            logger.warning(f"提取表格失败: {e}")

    def parse_table(self, table, table_index):
        """解析表格"""
        try:
            rows = table.find_all('tr')
            if not rows:
                return None
            
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': []
            }
            
            # 提取表头
            if rows:
                header_cells = rows[0].find_all(['th', 'td'])
                headers = [cell.get_text(strip=True) for cell in header_cells]
                table_data['headers'] = headers
            
            # 提取数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                row_data = [cell.get_text(strip=True) for cell in cells]
                if any(row_data):
                    table_data['data'].append(row_data)
            
            return table_data if table_data['data'] else None
            
        except Exception as e:
            logger.warning(f"解析表格失败: {e}")
            return None

    def save_data(self):
        """保存数据"""
        if not self.extracted_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('decode_oil_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        try:
            with pd.ExcelWriter('decode_oil_data.xlsx', engine='openpyxl') as writer:
                # 汇总表
                summary_data = []
                for item in self.extracted_data:
                    summary_data.append({
                        '地区': item.get('region', ''),
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '提取状态': item.get('extraction_status', ''),
                        '内容长度': item.get('content_length', 0),
                        '表格数量': len(item.get('tables', [])),
                        '今日摘要': item.get('summary_content', '')[:300],
                        '市场概况': item.get('market_overview', '')[:300],
                        '链接': item['url']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总', index=False)
                
                # 详细内容表
                detail_data = []
                for item in self.extracted_data:
                    detail_data.append({
                        '地区': item.get('region', ''),
                        '标题': item['title'],
                        '完整摘要': item.get('summary_content', ''),
                        '完整概况': item.get('market_overview', '')
                    })
                
                detail_df = pd.DataFrame(detail_data)
                detail_df.to_excel(writer, sheet_name='详细内容', index=False)
                
                # 表格数据
                for i, item in enumerate(self.extracted_data):
                    for j, table in enumerate(item.get('tables', [])):
                        if table.get('data'):
                            try:
                                df = pd.DataFrame(table['data'], columns=table.get('headers', []))
                                sheet_name = f"{item.get('region', 'Unknown')}_表格{j+1}"[:31]
                                df.to_excel(writer, sheet_name=sheet_name, index=False)
                            except Exception as e:
                                logger.warning(f"保存表格失败: {e}")
        except Exception as e:
            logger.error(f"保存Excel失败: {e}")
        
        # 创建报告
        self.create_report()

    def create_report(self):
        """创建报告"""
        total = len(self.extracted_data)
        success = sum(1 for item in self.extracted_data if item.get('extraction_status') == 'success')
        login_required = sum(1 for item in self.extracted_data if item.get('extraction_status') == 'login_required')
        encrypted = sum(1 for item in self.extracted_data if item.get('extraction_status') == 'encrypted')
        error = sum(1 for item in self.extracted_data if item.get('extraction_status') == 'error')
        
        has_summary = sum(1 for item in self.extracted_data 
                         if item.get('summary_content') and 
                         len(item.get('summary_content', '')) > 50 and
                         '失败' not in item.get('summary_content', '') and
                         '出错' not in item.get('summary_content', ''))
        
        has_overview = sum(1 for item in self.extracted_data 
                          if item.get('market_overview') and 
                          len(item.get('market_overview', '')) > 50)
        
        total_tables = sum(len(item.get('tables', [])) for item in self.extracted_data)
        
        report = f"""# 解码版成品油日评数据爬取报告

## 爬取概况
- 爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
- 总页面数: {total}
- 成功提取: {success}
- 需要登录: {login_required}
- 内容加密: {encrypted}
- 提取错误: {error}
- 有效摘要: {has_summary}
- 有效概况: {has_overview}
- 总表格数: {total_tables}

## 详细结果
"""
        
        for i, item in enumerate(self.extracted_data, 1):
            report += f"\n### {i}. {item.get('region', '未知')}地区\n"
            report += f"- 标题: {item['title']}\n"
            report += f"- 日期: {item.get('date', '未知')}\n"
            report += f"- 提取状态: {item.get('extraction_status', '未知')}\n"
            report += f"- 内容长度: {item.get('content_length', 0)} 字符\n"
            report += f"- 表格数量: {len(item.get('tables', []))}\n"
            
            if (item.get('summary_content') and 
                len(item.get('summary_content', '')) > 50 and
                '失败' not in item.get('summary_content', '') and
                '出错' not in item.get('summary_content', '')):
                report += f"- 今日摘要: {item['summary_content'][:200]}...\n"
            else:
                report += f"- 今日摘要: 未获取到有效内容\n"
            
            if item.get('market_overview') and len(item.get('market_overview', '')) > 50:
                report += f"- 市场概况: {item['market_overview'][:200]}...\n"
            else:
                report += f"- 市场概况: 未获取到有效内容\n"
            
            report += f"- 链接: {item['url']}\n"
        
        report += f"""
## 技术分析

1. **内容解码**: 
   - 尝试了多种字符编码方式
   - 尝试了gzip和zlib解压缩
   - 尝试了base64和URL解码

2. **数据质量**: 
   - 有效摘要率: {has_summary}/{total} ({has_summary/total*100:.1f}%)
   - 有效概况率: {has_overview}/{total} ({has_overview/total*100:.1f}%)

3. **主要问题**:
   - 网站内容可能采用了特殊的加密或压缩方式
   - 部分页面需要有效的会员登录
   - 反爬虫机制较为严格

4. **建议**:
   - 考虑使用更高级的解密技术
   - 完善登录认证流程
   - 使用浏览器自动化工具绕过JavaScript限制
"""
        
        with open('decode_oil_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

    def run(self):
        """运行解码爬虫"""
        logger.info("开始运行解码版成品油日评爬虫...")
        
        # 1. 尝试登录
        self.try_login()
        
        # 2. 处理每个页面
        for i, link_info in enumerate(self.daily_review_links):
            logger.info(f"处理第 {i+1}/{len(self.daily_review_links)} 个页面")
            
            page_data = self.extract_page_data(
                link_info['url'], 
                link_info['title'], 
                link_info['region']
            )
            
            if page_data:
                self.extracted_data.append(page_data)
            
            time.sleep(3)
        
        # 3. 保存数据
        self.save_data()
        
        logger.info(f"解码爬虫完成！共处理了 {len(self.extracted_data)} 个页面")
        logger.info("数据已保存到 decode_oil_data.json 和 decode_oil_data.xlsx")
        logger.info("详细报告已保存到 decode_oil_report.txt")

if __name__ == "__main__":
    spider = DecodeOilSpider()
    spider.run()
