#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整版成品油日评爬虫
使用已知的日评链接，专门处理数据提取
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteOilSpider:
    def __init__(self):
        # 基于调试结果的已知日评链接
        self.daily_review_links = [
            {
                'title': '[华东成品油日评]：成品油零售价搁浅不调 华东汽柴批发价偏弱调整（20250728）',
                'url': 'https://www.oilchem.net/25-0728-16-45626e42b48f2085.html',
                'region': '华东'
            },
            {
                'title': '[华北成品油日评]：国际油价支撑减弱 华北汽柴油价格下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-8c0315b83e481f64.html',
                'region': '华北'
            },
            {
                'title': '[华南成品油日评]：主营赶月度任务为主 汽柴小幅下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-15-5ca19894c073363d.html',
                'region': '华南'
            },
            {
                'title': '[华中成品油日评]：成交疲态未改 汽柴价格继续承压（20250728）',
                'url': 'https://www.oilchem.net/25-0728-14-45b942d25d40b230.html',
                'region': '华中'
            },
            {
                'title': '[西南成品油日评]：周末市场出货表现欠佳 汽柴价格承压下跌（20250728）',
                'url': 'https://www.oilchem.net/25-0728-14-02fa6ae11f7e16a6.html',
                'region': '西南'
            }
        ]
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        self.extracted_data = []

    def extract_page_data(self, url, title, region):
        """提取页面数据"""
        logger.info(f"正在提取: {title}")
        
        try:
            response = self.session.get(url, timeout=30)
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            page_data = {
                'title': title,
                'url': url,
                'region': region,
                'date': self.extract_date_from_title(title),
                'login_required': False,
                'summary_content': '',
                'market_overview': '',
                'tables': [],
                'page_info': {},
                'raw_content': ''
            }
            
            # 检查是否需要登录
            if self.check_login_required(soup):
                page_data['login_required'] = True
                page_data['summary_content'] = '需要登录才能查看完整内容'
                
                # 提取页面基本信息
                self.extract_page_info(soup, page_data)
                
                logger.warning(f"页面需要登录: {title}")
            else:
                # 提取完整内容
                self.extract_full_content(soup, page_data)
                logger.info(f"成功提取内容: {title}")
            
            return page_data
            
        except Exception as e:
            logger.error(f"提取页面数据失败: {e}")
            return None

    def check_login_required(self, soup):
        """检查是否需要登录"""
        login_indicators = [
            '会员登录', '免费开通', '手机号码', '短信验证码',
            '注册为会员', '致电资讯热线', '400-658-1688'
        ]
        
        page_text = soup.get_text()
        for indicator in login_indicators:
            if indicator in page_text:
                return True
        return False

    def extract_date_from_title(self, title):
        """从标题中提取日期"""
        date_pattern = r'(\d{8})'
        match = re.search(date_pattern, title)
        if match:
            date_str = match.group(1)
            # 格式化日期
            formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            return formatted_date
        return None

    def extract_page_info(self, soup, page_data):
        """提取页面基本信息"""
        try:
            # 提取页面标题
            title_elem = soup.find('title')
            if title_elem:
                page_data['page_info']['page_title'] = title_elem.get_text().strip()
            
            # 提取发布时间
            time_patterns = [
                r'发布时间[：:]\s*(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})',
                r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})',
                r'(\d{4}年\d{1,2}月\d{1,2}日)'
            ]
            
            page_text = soup.get_text()
            for pattern in time_patterns:
                match = re.search(pattern, page_text)
                if match:
                    page_data['page_info']['publish_time'] = match.group(1)
                    break
            
            # 提取可见的关键信息
            keywords = ['汽油', '柴油', '价格', '市场', '成品油', '批发', '零售']
            visible_info = []
            
            for keyword in keywords:
                if keyword in page_text:
                    # 查找包含关键词的句子
                    sentences = re.findall(f'[^。！？]*{keyword}[^。！？]*[。！？]', page_text)
                    for sentence in sentences[:2]:  # 只取前2个句子
                        if len(sentence) > 10:
                            visible_info.append(sentence.strip())
            
            page_data['page_info']['visible_keywords'] = visible_info
            
        except Exception as e:
            logger.warning(f"提取页面信息失败: {e}")

    def extract_full_content(self, soup, page_data):
        """提取完整内容"""
        try:
            # 获取页面文本
            page_text = soup.get_text()
            page_data['raw_content'] = page_text[:1000]  # 保存前1000字符用于调试
            
            # 清理文本
            cleaned_text = self.clean_text(page_text)
            
            # 提取今日摘要到市场概要的内容
            summary_patterns = [
                r'今日摘要[：:]([^市场概要]*?)(?=市场概要|$)',
                r'摘要[：:]([^市场]*?)(?=市场|$)',
                r'概述[：:]([^市场]*?)(?=市场|$)'
            ]
            
            for pattern in summary_patterns:
                match = re.search(pattern, cleaned_text, re.DOTALL | re.IGNORECASE)
                if match:
                    summary = match.group(1).strip()
                    # 清理和格式化
                    summary = re.sub(r'\s+', ' ', summary)
                    summary = summary.replace('\n', ' ').replace('\r', ' ')
                    page_data['summary_content'] = summary[:800]  # 限制长度
                    break
            
            # 提取市场概要
            overview_patterns = [
                r'市场概要[：:]([^表格价格]*?)(?=表格|价格|结论|$)',
                r'市场分析[：:]([^表格价格]*?)(?=表格|价格|结论|$)',
                r'市场情况[：:]([^表格价格]*?)(?=表格|价格|结论|$)'
            ]
            
            for pattern in overview_patterns:
                match = re.search(pattern, cleaned_text, re.DOTALL | re.IGNORECASE)
                if match:
                    overview = match.group(1).strip()
                    overview = re.sub(r'\s+', ' ', overview)
                    overview = overview.replace('\n', ' ').replace('\r', ' ')
                    page_data['market_overview'] = overview[:800]
                    break
            
            # 如果没有找到特定内容，提取主要段落
            if not page_data['summary_content']:
                main_content = self.extract_main_content(cleaned_text)
                page_data['summary_content'] = main_content
            
            # 提取表格
            self.extract_tables(soup, page_data)
            
            # 提取价格信息
            self.extract_price_info(cleaned_text, page_data)
            
        except Exception as e:
            logger.warning(f"提取完整内容失败: {e}")

    def clean_text(self, text):
        """清理文本"""
        # 移除HTML实体
        text = re.sub(r'&[a-zA-Z0-9#]+;', '', text)
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符但保留中文标点
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,，。：:；;！!？?\(\)\[\]（）【】\-\+%￥]', '', text)
        return text.strip()

    def extract_main_content(self, text):
        """提取主要内容"""
        # 按句子分割
        sentences = re.split(r'[。！？]', text)
        
        # 过滤有意义的句子
        meaningful_sentences = []
        keywords = ['汽油', '柴油', '价格', '市场', '成品油', '批发', '零售', '调整', '上涨', '下跌']
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 15 and any(keyword in sentence for keyword in keywords):
                if not any(exclude in sentence for exclude in ['登录', '注册', '验证码', '客服']):
                    meaningful_sentences.append(sentence)
        
        return '。'.join(meaningful_sentences[:3]) + '。' if meaningful_sentences else ''

    def extract_price_info(self, text, page_data):
        """提取价格信息"""
        try:
            # 查找价格相关信息
            price_patterns = [
                r'(\d+)\s*元/吨',
                r'(\d+)\s*元/升',
                r'上涨\s*(\d+)',
                r'下跌\s*(\d+)',
                r'调整\s*(\d+)'
            ]
            
            price_info = []
            for pattern in price_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    price_info.append(match)
            
            if price_info:
                page_data['page_info']['price_numbers'] = price_info[:10]  # 限制数量
                
        except Exception as e:
            logger.warning(f"提取价格信息失败: {e}")

    def extract_tables(self, soup, page_data):
        """提取表格"""
        try:
            tables = soup.find_all('table')
            logger.info(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables[:3]):  # 只取前三张表
                table_data = self.parse_table(table, i + 1)
                if table_data:
                    page_data['tables'].append(table_data)
            
            logger.info(f"成功解析 {len(page_data['tables'])} 张表格")
            
        except Exception as e:
            logger.warning(f"提取表格失败: {e}")

    def parse_table(self, table, table_index):
        """解析表格"""
        try:
            rows = table.find_all('tr')
            if not rows:
                return None
            
            table_data = {
                'table_index': table_index,
                'headers': [],
                'data': [],
                'description': f'第{table_index}张表格'
            }
            
            # 提取表头
            if rows:
                header_cells = rows[0].find_all(['th', 'td'])
                headers = []
                for cell in header_cells:
                    header_text = self.clean_text(cell.get_text())
                    headers.append(header_text if header_text else f'列{len(headers)+1}')
                table_data['headers'] = headers
            
            # 提取数据行
            for row in rows[1:]:
                cells = row.find_all(['td', 'th'])
                row_data = []
                for cell in cells:
                    cell_text = self.clean_text(cell.get_text())
                    row_data.append(cell_text)
                
                if any(row_data):  # 只添加非空行
                    table_data['data'].append(row_data)
            
            # 如果表格有数据，添加描述
            if table_data['data']:
                if any('价格' in str(header) for header in table_data['headers']):
                    table_data['description'] = f'第{table_index}张表格 - 价格相关数据'
                elif any('市场' in str(header) for header in table_data['headers']):
                    table_data['description'] = f'第{table_index}张表格 - 市场数据'
                
                return table_data
            
            return None
            
        except Exception as e:
            logger.warning(f"解析表格失败: {e}")
            return None

    def save_data(self):
        """保存数据"""
        if not self.extracted_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存为JSON
        with open('complete_oil_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, ensure_ascii=False, indent=2)
        
        # 保存为Excel
        try:
            with pd.ExcelWriter('complete_oil_data.xlsx', engine='openpyxl') as writer:
                # 汇总表
                summary_data = []
                for item in self.extracted_data:
                    summary_data.append({
                        '地区': item.get('region', ''),
                        '标题': item['title'],
                        '日期': item.get('date', ''),
                        '需要登录': '是' if item.get('login_required', False) else '否',
                        '表格数量': len(item.get('tables', [])),
                        '今日摘要': item.get('summary_content', '')[:150] + '...' if len(item.get('summary_content', '')) > 150 else item.get('summary_content', ''),
                        '市场概要': item.get('market_overview', '')[:150] + '...' if len(item.get('market_overview', '')) > 150 else item.get('market_overview', ''),
                        '链接': item['url']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总', index=False)
                
                # 表格数据
                for i, item in enumerate(self.extracted_data):
                    for j, table in enumerate(item.get('tables', [])):
                        if table.get('data'):
                            try:
                                df = pd.DataFrame(table['data'], columns=table.get('headers', []))
                                sheet_name = f"{item.get('region', 'Unknown')}_表格{j+1}"[:31]
                                df.to_excel(writer, sheet_name=sheet_name, index=False)
                            except Exception as e:
                                logger.warning(f"保存表格到Excel失败: {e}")
        except Exception as e:
            logger.error(f"保存Excel失败: {e}")
        
        # 创建详细报告
        self.create_detailed_report()

    def create_detailed_report(self):
        """创建详细报告"""
        total = len(self.extracted_data)
        login_required = sum(1 for item in self.extracted_data if item.get('login_required', False))
        accessible = total - login_required
        total_tables = sum(len(item.get('tables', [])) for item in self.extracted_data)
        
        report = f"""# 成品油日评数据爬取完整报告

## 爬取概况
- 爬取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
- 总页面数: {total}
- 需要登录: {login_required}
- 成功访问: {accessible}
- 总表格数: {total_tables}

## 按地区分类
"""
        
        # 按地区分组
        regions = {}
        for item in self.extracted_data:
            region = item.get('region', '未知')
            if region not in regions:
                regions[region] = []
            regions[region].append(item)
        
        for region, items in regions.items():
            report += f"\n### {region}地区\n"
            for item in items:
                status = "需要登录" if item.get('login_required', False) else "成功访问"
                report += f"- 状态: {status}\n"
                report += f"- 日期: {item.get('date', '未知')}\n"
                report += f"- 表格数: {len(item.get('tables', []))}\n"
                
                if not item.get('login_required'):
                    if item.get('summary_content'):
                        report += f"- 今日摘要: {item['summary_content'][:100]}...\n"
                    if item.get('market_overview'):
                        report += f"- 市场概要: {item['market_overview'][:100]}...\n"
                    
                    # 添加价格信息
                    price_info = item.get('page_info', {}).get('price_numbers', [])
                    if price_info:
                        report += f"- 价格数据: {', '.join(price_info[:5])}\n"
                
                report += f"- 链接: {item['url']}\n\n"
        
        # 添加表格信息
        if total_tables > 0:
            report += "\n## 表格信息\n"
            for i, item in enumerate(self.extracted_data):
                if item.get('tables'):
                    report += f"\n### {item.get('region', '未知')}地区表格\n"
                    for j, table in enumerate(item['tables']):
                        report += f"- {table.get('description', f'表格{j+1}')}\n"
                        report += f"  列数: {len(table.get('headers', []))}\n"
                        report += f"  行数: {len(table.get('data', []))}\n"
                        if table.get('headers'):
                            report += f"  列名: {', '.join(table['headers'][:5])}\n"
        
        with open('complete_oil_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

    def run(self):
        """运行爬虫"""
        logger.info("开始运行完整版成品油日评爬虫...")
        logger.info(f"将处理 {len(self.daily_review_links)} 个已知的日评页面")
        
        # 处理每个页面
        for i, link_info in enumerate(self.daily_review_links):
            logger.info(f"处理第 {i+1}/{len(self.daily_review_links)} 个页面")
            
            page_data = self.extract_page_data(
                link_info['url'], 
                link_info['title'], 
                link_info['region']
            )
            
            if page_data:
                self.extracted_data.append(page_data)
            
            time.sleep(2)  # 避免请求过快
        
        # 保存数据
        self.save_data()
        
        logger.info(f"爬虫完成！共处理了 {len(self.extracted_data)} 个页面")
        logger.info("数据已保存到 complete_oil_data.json 和 complete_oil_data.xlsx")
        logger.info("详细报告已保存到 complete_oil_report.txt")

if __name__ == "__main__":
    spider = CompleteOilSpider()
    spider.run()
